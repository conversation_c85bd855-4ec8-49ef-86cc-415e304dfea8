# 数据结构转换实现文档

## 概述

本文档记录了根据 `newProperties` 的 schema 结构补充 `submitData` 字段的实现方案。

## 问题描述

原始的 `submitData` 中的数据结构不符合 Amazon API 的 schema 规范。例如：

### 问题示例 - container 字段

**期望的正确结构：**
```json
{
  "container": [
    {
      "marketplace_id": "ATVPDKIKX0DER",
      "language_tag": "en_US",
      "material": [{
        "value": "paper",
        "marketplace_id": "ATVPDKIKX0DER",
        "language_tag": "en_US"
      }],
      "shape": [{
        "value": "Cube",
        "language_tag": "en_US"
      }],
      "type": [{
        "value": "Bag",
        "language_tag": "en_US"
      }]
    }
  ]
}
```

**实际的错误结构：**
```json
{
  "container": [
    {
      "marketplace_id": "ATVPDKIKX0DER",
      "language_tag": "en_US",
      "material": {
        "value": "paper"
      },
      "shape": {
        "value": "Cube"
      },
      "type": {
        "value": "Bag"
      }
    }
  ]
}
```

## 解决方案

### 1. 新增核心转换函数

在 `amazon.js` 中添加了以下函数：

#### `transformSubmitDataBySchema(submitData, newProperties)`
- **功能**: 主转换函数，根据 schema 结构转换整个 submitData
- **参数**: 
  - `submitData`: 原始提交数据
  - `newProperties`: schema 属性定义数组
- **返回**: 转换后的数据对象

#### `processFieldBySchema(fieldValue, schemaDefinition, fieldName, marketplaceId, languageTag)`
- **功能**: 处理单个字段的转换
- **参数**: 字段值、schema 定义、字段名、默认值等
- **返回**: 处理后的字段值

#### `ensureArrayStructure(value, itemsSchema, marketplaceId, languageTag)`
- **功能**: 确保数组结构符合 schema 规范
- **核心逻辑**: 
  - 将 `{value: "xxx"}` 转换为 `[{value: "xxx", marketplace_id: "xxx", language_tag: "xxx"}]`
  - 补充缺失的 `marketplace_id` 和 `language_tag` 字段
  - 处理嵌套数组结构

### 2. 集成到现有代码

在 `index.html` 中的数据处理部分添加了转换调用：

```javascript
// 使用新的转换函数处理 submitData，确保数据结构符合 schema 规范
let transformedSubmitData = amazonTransformSubmitDataBySchema(submitData, newProperties);
console.log("转换前的 submitData:", submitData);
console.log("转换后的 transformedSubmitData:", transformedSubmitData);
```

## 转换规则

1. **数组字段处理**: 如果 schema 定义字段为数组类型，确保数据也是数组格式
2. **必需字段补充**: 自动添加缺失的 `marketplace_id` 和 `language_tag` 字段
3. **嵌套结构处理**: 递归处理嵌套的数组和对象结构
4. **数据完整性**: 保持原有数据不变，只补充缺失的结构

## 使用方法

```javascript
// 调用转换函数
const transformedData = amazonTransformSubmitDataBySchema(originalSubmitData, schemaProperties);

// 或使用全局函数
const transformedData = amazonTransformSubmitDataBySchema(originalSubmitData, schemaProperties);
```

## 测试验证

转换函数会在控制台输出转换前后的数据对比，便于验证转换效果：

- `转换前的 submitData`: 显示原始数据结构
- `转换后的 transformedSubmitData`: 显示符合规范的数据结构

## 注意事项

1. 函数会创建数据的深拷贝，不会修改原始数据
2. 默认使用 `ATVPDKIKX0DER` 作为 marketplace_id，`en_US` 作为 language_tag
3. 只处理在 `newProperties` 中定义的字段
4. 保持向后兼容性，不影响现有功能

## 扩展性

该实现具有良好的扩展性，可以轻松添加新的字段转换规则和处理逻辑。
