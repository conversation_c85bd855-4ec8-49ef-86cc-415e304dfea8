<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>DropList组件</title>
  <style>
    .dropdown {
      position: relative;
      display: inline-block;
    }

    .dropdown-btn {
      padding: 10px 20px;
      background-color: #4CAF50;
      color: white;
      border: none;
      cursor: pointer;
      border-radius: 4px;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .dropdown-btn::after {
      content: '▼';
      font-size: 12px;
      transition: transform 0.3s ease;
    }

    .dropdown:hover .dropdown-btn::after {
      transform: rotate(180deg);
    }

    .dropdown-content {
      visibility: hidden;
      opacity: 0;
      position: absolute;
      background-color: #f9f9f9;
      min-width: 160px;
      box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
      z-index: 1;
      border-radius: 4px;
      margin-top: 5px;
      transition: all 0.3s ease;
      pointer-events: none;
    }

    .dropdown-content a {
      color: black;
      padding: 12px 16px;
      text-decoration: none;
      display: block;
    }

    .dropdown-content a:hover {
      background-color: #f1f1f1;
    }

    .dropdown:hover .dropdown-content {
      visibility: visible;
      opacity: 1;
      pointer-events: auto;
    }
  </style>
</head>

<body>
  <div class="dropdown">
    <div id="dropdownContainer"></div>
  </div>
  <script src="index.js"></script>
</body>

</html>