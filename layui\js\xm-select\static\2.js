/*!
 * @Title: xm-select
 * @Version: 1.2.4
 * @Description：基于layui的多选解决方案
 * @Site: https://gitee.com/maplemei/xm-select
 * @Author: maplemei
 * @License：Apache License 2.0
 */
(window.webpackJsonp=window.webpackJsonp||[]).push([[2],{694:function(t,e,n){t.exports=n.p+"static/wx.b556b2e.jpg"},695:function(t,e){t.exports="data:image/png;base64,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"},697:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),t._m(2),n("demo-block",[n("div",[n("p",[n("code",[t._v("el")]),t._v("绑定的不一定是id, 也可以是其他css选择器")])]),n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(3),n("demo-block",[n("div",[n("p",[n("code",[t._v("el")]),t._v("绑定的不一定是id, 也可以是dom元素, 不支持jQuery dom对象")])]),n("template",{slot:"source"},[n("element-demo1")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: document.querySelector('#demo2'), \n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:document.querySelector("#demo2"),data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"ji-chu-shi-yong"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#ji-chu-shi-yong"}},[this._v("¶")]),this._v(" 基础使用")])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"tip"},[e("p",[this._v("只需引入"),e("code",[this._v("xm-select.js")]),this._v(", 剩下的就只有渲染了")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"yi-ge-xiao-li-zi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#yi-ge-xiao-li-zi"}},[this._v("¶")]),this._v(" 一个小栗子")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"yongdom-jin-xing-xuan-ran"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#yongdom-jin-xing-xuan-ran"}},[this._v("¶")]),this._v(" 用dom进行渲染")])}],!1,null,null,null);r.options.__file="docs/mds/XM01.md";e.default=r.exports},698:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),n("demo-block",[n("div",[n("p",[t._v("目前仅支持"),n("code",[t._v("中文")]),t._v("和"),n("code",[t._v("英文")]),t._v(", 如需更多语言, 可以"),n("code",[t._v("clone")]),t._v("代码进行二次开发")])]),n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n<div xid=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tlanguage: 'en',\n\tdata: []\n})\nvar demo2 = xmSelect.render({\n\tel: '[xid=demo2]', \n\tlanguage: 'en',\n\tdata: [\n\t\t{name: 'apple', value: 1},\n\t\t{name: 'banana', value: 2},\n\t\t{name: 'orange', value: 3},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}}),this._v(" "),e("div",{staticClass:"xm-select-demo",attrs:{xid:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",language:"en",data:[]}),xmSelect.render({el:"[xid=demo2]",language:"en",data:[{name:"apple",value:1},{name:"banana",value:2},{name:"orange",value:3}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"guo-ji-hua"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#guo-ji-hua"}},[this._v("¶")]),this._v(" 国际化")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"ying-yu"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#ying-yu"}},[this._v("¶")]),this._v(" 英语")])}],!1,null,null,null);r.options.__file="docs/mds/XM02.md";e.default=r.exports},699:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),n("demo-block",[n("div",[n("p",[t._v("当然"),n("code",[t._v("selected")]),t._v("是选中, "),n("code",[t._v("disabled")]),t._v("是禁用")])]),n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\tdata: [\n\t\t{name: '水果', value: 1, selected: true, disabled: true},\n\t\t{name: '蔬菜', value: 2, selected: true},\n\t\t{name: '桌子', value: 3, disabled: true},\n\t\t{name: '北京', value: 4},\n\t]\n})\n<\/script>\n")])])])],2),t._m(2),n("demo-block",[n("div",[n("p",[n("code",[t._v("initValue")]),t._v("的优先级大于选项中的"),n("code",[t._v("selected")])])]),n("template",{slot:"source"},[n("element-demo1")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo5 = xmSelect.render({\n\tel: '#demo1', \n\tinitValue: [4],\n\tdata: [\n\t\t{name: '水果', value: 1, selected: true, disabled: true},\n\t\t{name: '蔬菜', value: 2, selected: true},\n\t\t{name: '桌子', value: 3, disabled: true},\n\t\t{name: '北京', value: 4},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",data:[{name:"水果",value:1,selected:!0,disabled:!0},{name:"蔬菜",value:2,selected:!0},{name:"桌子",value:3,disabled:!0},{name:"北京",value:4}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",initValue:[4],data:[{name:"水果",value:1,selected:!0,disabled:!0},{name:"蔬菜",value:2,selected:!0},{name:"桌子",value:3,disabled:!0},{name:"北京",value:4}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"mo-ren-xuan-zhong"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#mo-ren-xuan-zhong"}},[this._v("¶")]),this._v(" 默认选中")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"shi-yongselected-shu-xing"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#shi-yongselected-shu-xing"}},[this._v("¶")]),this._v(" 使用"),e("code",[this._v("selected")]),this._v("属性")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"shi-yonginitvalue-jin-xing-chu-shi-hua"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#shi-yonginitvalue-jin-xing-chu-shi-hua"}},[this._v("¶")]),this._v(" 使用initValue进行初始化")])}],!1,null,null,null);r.options.__file="docs/mds/XM03.md";e.default=r.exports},700:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\ttips: '你喜欢什么水果呢?',\n\tdata: [\n\t\t{name: '水果', value: 1},\n\t\t{name: '蔬菜', value: 2},\n\t\t{name: '桌子', value: 3},\n\t\t{name: '北京', value: 4},\n\t]\n})\n<\/script>\n")])])])],2),this._m(2),e("demo-block",[e("template",{slot:"source"},[e("element-demo1")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\tempty: '呀, 没有数据呢',\n\tdata: [ ]\n})\n<\/script>\n")])])])],2),this._m(3),e("demo-block",[e("template",{slot:"source"},[e("element-demo2")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo3\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo3 = xmSelect.render({\n\tel: '#demo3', \n\tfilterable: true,\n\tsearchTips: '你想吃什么水果吧',\n\tdata: [\n\t\t{name: '水果', value: 1},\n\t\t{name: '蔬菜', value: 2},\n\t\t{name: '桌子', value: 3},\n\t\t{name: '北京', value: 4},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",tips:"你喜欢什么水果呢?",data:[{name:"水果",value:1},{name:"蔬菜",value:2},{name:"桌子",value:3},{name:"北京",value:4}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",empty:"呀, 没有数据呢",data:[]})}))}}}(),"element-demo2":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo3"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo3",filterable:!0,searchTips:"你想吃什么水果吧",data:[{name:"水果",value:1},{name:"蔬菜",value:2},{name:"桌子",value:3},{name:"北京",value:4}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"xiu-gai-ti-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xiu-gai-ti-shi"}},[this._v("¶")]),this._v(" 修改提示")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"xiu-gai-xuan-xiang-ti-shi-wen-zi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xiu-gai-xuan-xiang-ti-shi-wen-zi"}},[this._v("¶")]),this._v(" 修改选项提示文字")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"xiu-gai-kong-shu-ju-ti-shi-wen-zi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xiu-gai-kong-shu-ju-ti-shi-wen-zi"}},[this._v("¶")]),this._v(" 修改空数据提示文字")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"xiu-gai-sou-suo-ti-shi-wen-zi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xiu-gai-sou-suo-ti-shi-wen-zi"}},[this._v("¶")]),this._v(" 修改搜索提示文字")])}],!1,null,null,null);r.options.__file="docs/mds/XM04.md";e.default=r.exports},701:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),n("demo-block",[n("div",[n("p",[t._v("默认按照"),n("code",[t._v("name")]),t._v("进行搜索")])]),n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tfilterable: true,\n\tdata: [\n\t\t{name: '水果', value: 1},\n\t\t{name: '蔬菜', value: 2},\n\t\t{name: '桌子', value: 3},\n\t\t{name: '北京', value: 4},\n\t]\n})\n<\/script>\n")])])])],2),t._m(2),n("demo-block",[n("template",{slot:"source"},[n("element-demo1")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\tfilterable: true,\n\tfilterMethod: function(val, item, index, prop){\n\t\tif(val == item.value){//把value相同的搜索出来\n\t\t\treturn true;\n\t\t}\n\t\tif(item.name.indexOf(val) != -1){//名称中包含的搜索出来\n\t\t\treturn true;\n\t\t}\n\t\treturn false;//不知道的就不管了\n\t},\n\tdata: [\n\t\t{name: '水果', value: 1},\n\t\t{name: '蔬菜', value: 2},\n\t\t{name: '桌子', value: 3},\n\t\t{name: '北京', value: 4},\n\t]\n})\n<\/script>\n")])])])],2),t._m(3),t._m(4),n("demo-block",[n("div",[n("p",[n("code",[t._v("delay: 2000")]),t._v(" 输入停止2s后进行搜索过滤")])]),n("template",{slot:"source"},[n("element-demo2")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo3\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo3 = xmSelect.render({\n\tel: '#demo3', \n\tfilterable: true,\n\tdelay: 2000,\n\tdata: [\n\t\t{name: '水果', value: 1},\n\t\t{name: '蔬菜', value: 2},\n\t\t{name: '桌子', value: 3},\n\t\t{name: '北京', value: 4},\n\t]\n})\n<\/script>\n")])])])],2),t._m(5),t._m(6),t._m(7),n("p",[t._v("第三部: 重写搜索回调")]),n("p",[t._v("简单吧, 记得Star ^_^")]),n("demo-block",[n("div",[n("p",[n("code",[t._v("render")]),t._v("后, 就会进行一次回调, 用于渲染第一次数据")])]),n("template",{slot:"source"},[n("element-demo3")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo4\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo4 = xmSelect.render({\n\tel: '#demo4', \n\tfilterable: true,\n\tremoteSearch: true,\n\tremoteMethod: function(val, cb, show){\n\t\t//这里模拟3s后返回数据\n\t\tsetTimeout(function(){\n\t\t\t//需要回传一个数组\n\t\t\tcb([\n\t\t\t\t{name: '水果' + val, value: val + 1},\n\t\t\t\t{name: '蔬菜' + val, value: val + 2, selected: true},\n\t\t\t\t{name: '桌子' + val, value: val + 3},\n\t\t\t\t{name: '北京' + val, value: val + 4},\n\t\t\t])\n\t\t}, 3000)\n\t},\n\tdata: []\n})\n<\/script>\n")])])])],2),t._m(8),t._m(9),t._m(10),n("p",[t._v("第三部: 重写搜索回调")]),n("p",[t._v("简单吧, 记得Star ^_^")]),n("demo-block",[n("div",[n("p",[n("code",[t._v("render")]),t._v("后, 就会进行一次回调, 用于渲染第一次数据")])]),n("template",{slot:"source"},[n("element-demo4")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo5\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo5 = xmSelect.render({\n\tel: '#demo5', \n\tfilterable: true,\n\tremoteSearch: true,\n\tremoteMethod: function(val, cb, show){\n\t\taxios({\n\t\t\tmethod: 'get',\n\t\t\turl: 'https://www.fastmock.site/mock/98228b1f16b7e5112d6c0c87921eabc1/xmSelect/search',\n\t\t\tparams: {\n\t\t\t\tkeyword: val,\n\t\t\t}\n\t\t}).then(response => {\n\t\t\tvar res = response.data;\n\t\t\tcb(res.data)\n\t\t}).catch(err => {\n\t\t\tcb([]);\n\t\t});\n\t},\n\tdata: []\n})\n<\/script>\n")])])])],2),t._m(11),n("demo-block",[n("template",{slot:"source"},[n("element-demo5")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo6\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo6 = xmSelect.render({\n\tel: '#demo6', \n\tfilterable: true,\n\tfilterDone: function(val, list){\n\t\talert(`搜索完毕, 搜索关键词: ${val}, 过滤数据: ${list.length}个`)\n\t},\n\tdata: [\n\t\t{name: '水果', value: 1},\n\t\t{name: '蔬菜', value: 2},\n\t\t{name: '桌子', value: 3},\n\t\t{name: '北京', value: 4},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",filterable:!0,data:[{name:"水果",value:1},{name:"蔬菜",value:2},{name:"桌子",value:3},{name:"北京",value:4}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",filterable:!0,filterMethod:function(t,e,n,a){return t==e.value||-1!=e.name.indexOf(t)},data:[{name:"水果",value:1},{name:"蔬菜",value:2},{name:"桌子",value:3},{name:"北京",value:4}]})}))}}}(),"element-demo2":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo3"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo3",filterable:!0,delay:2e3,data:[{name:"水果",value:1},{name:"蔬菜",value:2},{name:"桌子",value:3},{name:"北京",value:4}]})}))}}}(),"element-demo3":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo4"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo4",filterable:!0,remoteSearch:!0,remoteMethod:function(t,e,n){setTimeout((function(){e([{name:"水果"+t,value:t+1},{name:"蔬菜"+t,value:t+2,selected:!0},{name:"桌子"+t,value:t+3},{name:"北京"+t,value:t+4}])}),3e3)},data:[]})}))}}}(),"element-demo4":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo5"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo5",filterable:!0,remoteSearch:!0,remoteMethod:function(t,e,n){axios({method:"get",url:"https://www.fastmock.site/mock/98228b1f16b7e5112d6c0c87921eabc1/xmSelect/search",params:{keyword:t}}).then((function(t){var n=t.data;e(n.data)})).catch((function(t){e([])}))},data:[]})}))}}}(),"element-demo5":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo6"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo6",filterable:!0,filterDone:function(t,e){alert("搜索完毕, 搜索关键词: ".concat(t,", 过滤数据: ").concat(e.length,"个"))},data:[{name:"水果",value:1},{name:"蔬菜",value:2},{name:"桌子",value:3},{name:"北京",value:4}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"sou-suo-mo-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#sou-suo-mo-shi"}},[this._v("¶")]),this._v(" 搜索模式")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"mo-ren-sou-suo"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#mo-ren-sou-suo"}},[this._v("¶")]),this._v(" 默认搜索")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"chong-xie-sou-suo-fang-fa"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#chong-xie-sou-suo-fang-fa"}},[this._v("¶")]),this._v(" 重写搜索方法")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"sou-suo-yan-chi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#sou-suo-yan-chi"}},[this._v("¶")]),this._v(" 搜索延迟")])},function(){var t=this.$createElement,e=this._self._c||t;return e("p",[this._v("为了提高有效搜索, 当停止输入"),e("code",[this._v("500ms")]),this._v("后才开始进行过滤搜索, 当然这个"),e("code",[this._v("500")]),this._v("你也可以进行修改")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"zi-ding-yi-sou-suo-yuan-cheng-sou-suo"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zi-ding-yi-sou-suo-yuan-cheng-sou-suo"}},[this._v("¶")]),this._v(" 自定义搜索 (远程搜索)")])},function(){var t=this.$createElement,e=this._self._c||t;return e("p",[this._v("第一步: 需要先开启搜索 "),e("code",[this._v("filterable: true,")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("p",[this._v("第二步: 开启自定义搜索 "),e("code",[this._v("remoteSearch: true")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"yuan-cheng-sou-suo"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#yuan-cheng-sou-suo"}},[this._v("¶")]),this._v(" 远程搜索")])},function(){var t=this.$createElement,e=this._self._c||t;return e("p",[this._v("第一步: 需要先开启搜索 "),e("code",[this._v("filterable: true,")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("p",[this._v("第二步: 开启自定义搜索 "),e("code",[this._v("remoteSearch: true")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"sou-suo-wan-cheng-hui-diao"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#sou-suo-wan-cheng-hui-diao"}},[this._v("¶")]),this._v(" 搜索完成回调")])}],!1,null,null,null);r.options.__file="docs/mds/XM05.md";e.default=r.exports},702:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div style=\"height: 500px\">占位div, 演示效果使用, 底部空间不足时会自动向上展开</div>\n\n<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tdirection: 'auto',\n\tdata: [\n\t\t{name: '水果', value: 1},\n\t\t{name: '蔬菜', value: 2},\n\t\t{name: '桌子', value: 3},\n\t\t{name: '北京', value: 4},\n\t]\n})\n<\/script>\n")])])])],2),this._m(2),e("demo-block",[e("template",{slot:"source"},[e("element-demo1")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\tdirection: 'up',\n\tdata: [\n\t\t{name: '水果', value: 1},\n\t\t{name: '蔬菜', value: 2},\n\t\t{name: '桌子', value: 3},\n\t\t{name: '北京', value: 4},\n\t]\n})\n<\/script>\n")])])])],2),this._m(3),e("demo-block",[e("template",{slot:"source"},[e("element-demo2")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo3\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo3 = xmSelect.render({\n\tel: '#demo3', \n\tdirection: 'down',\n\tdata: [\n\t\t{name: '水果', value: 1},\n\t\t{name: '蔬菜', value: 2},\n\t\t{name: '桌子', value: 3},\n\t\t{name: '北京', value: 4},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticStyle:{height:"500px"}},[this._v("占位div, 演示效果使用, 底部空间不足时会自动向上展开")]),this._v(" "),e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",direction:"auto",data:[{name:"水果",value:1},{name:"蔬菜",value:2},{name:"桌子",value:3},{name:"北京",value:4}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",direction:"up",data:[{name:"水果",value:1},{name:"蔬菜",value:2},{name:"桌子",value:3},{name:"北京",value:4}]})}))}}}(),"element-demo2":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo3"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo3",direction:"down",data:[{name:"水果",value:1},{name:"蔬菜",value:2},{name:"桌子",value:3},{name:"北京",value:4}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"xia-la-fang-xiang"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xia-la-fang-xiang"}},[this._v("¶")]),this._v(" 下拉方向")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"zi-dongauto"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zi-dongauto"}},[this._v("¶")]),this._v(" 自动"),e("code",[this._v("auto")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"da-qia-xiang-shangup"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#da-qia-xiang-shangup"}},[this._v("¶")]),this._v(" 打卡向上"),e("code",[this._v("up")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"da-kai-xiang-xiadown"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#da-kai-xiang-xiadown"}},[this._v("¶")]),this._v(" 打开向下"),e("code",[this._v("down")])])}],!1,null,null,null);r.options.__file="docs/mds/XM06.md";e.default=r.exports},703:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),e("p",[this._v("修改一下外边距, 加上圆角, 更改一下高度")]),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tstyle: {\n\t\tmarginLeft: '200px',\n\t\tborderRadius: '50px',\n\t\theight: '50px',\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),this._m(2),e("demo-block",[e("template",{slot:"source"},[e("element-demo1")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\theight: '50px',\n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",style:{marginLeft:"200px",borderRadius:"50px",height:"50px"},data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",height:"50px",data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"zi-ding-yi-yang-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zi-ding-yi-yang-shi"}},[this._v("¶")]),this._v(" 自定义样式")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"sui-bian-shi-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#sui-bian-shi-shi"}},[this._v("¶")]),this._v(" 随便试试")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"xiu-gai-xia-la-kuang-de-zui-da-gao-du"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xiu-gai-xia-la-kuang-de-zui-da-gao-du"}},[this._v("¶")]),this._v(" 修改下拉框的最大高度")])}],!1,null,null,null);r.options.__file="docs/mds/XM07.md";e.default=r.exports},704:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),t._m(2),n("demo-block",[n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tpaging: true,\n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t\t{name: '赵六', value: 4},\n\t\t{name: '苹果', value: 5},\n\t\t{name: '香蕉', value: 6},\n\t\t{name: '凤梨', value: 7},\n\t\t{name: '葡萄', value: 8},\n\t\t{name: '樱桃', value: 9},\n\t\t{name: '车厘子', value: 10},\n\t\t{name: '火龙果', value: 11},\n\t]\n})\n<\/script>\n")])])])],2),t._m(3),n("p",[t._v("每页3条")]),t._m(4),n("demo-block",[n("template",{slot:"source"},[n("element-demo1")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\tpaging: true,\n\tpageSize: 3,\n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t\t{name: '赵六', value: 4},\n\t\t{name: '苹果', value: 5},\n\t\t{name: '香蕉', value: 6},\n\t\t{name: '凤梨', value: 7},\n\t\t{name: '葡萄', value: 8},\n\t\t{name: '樱桃', value: 9},\n\t\t{name: '车厘子', value: 10},\n\t\t{name: '火龙果', value: 11},\n\t]\n})\n<\/script>\n")])])])],2),t._m(5),t._m(6),n("demo-block",[n("template",{slot:"source"},[n("element-demo2")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v('<div id="demo3" class="xm-select-demo"></div>\n<button class="btn" id="demo3-5">每页5条</button>\n<button class="btn" id="demo3-10">每页10条</button>\n<button class="btn" id="demo3-20">每页20条</button>\n\n<script>\nvar data = [];\nfor(var i = 0 ; i < 100 ; i++ ){\n\tdata.push({\n\t\tname: \'测试数据\' + i,\n\t\tvalue: i,\n\t})\n}\n\nvar demo3 = xmSelect.render({\n\tel: \'#demo3\', \n\tpaging: true,\n\tpageSize: 5,\n\tfilterable: true,\n\tdata\n})\n\ndocument.getElementById(\'demo3-5\').onclick = function(){\n\tdemo3.update({\n\t\tpageSize: 5\n\t})\n}\n\ndocument.getElementById(\'demo3-10\').onclick = function(){\n\tdemo3.update({\n\t\tpageSize: 10\n\t})\n}\n\ndocument.getElementById(\'demo3-20\').onclick = function(){\n\tdemo3.update({\n\t\tpageSize: 20\n\t})\n}\n\n<\/script>\n')])])])],2),t._m(7),t._m(8),n("demo-block",[n("template",{slot:"source"},[n("element-demo3")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo4\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo4 = xmSelect.render({\n\tel: '#demo4', \n\tpaging: true,\n\tpageSize: 3,\n\tfilterable: true,\n\tpageEmptyShow: false,\n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t\t{name: '赵六', value: 4},\n\t\t{name: '苹果', value: 5},\n\t\t{name: '香蕉', value: 6},\n\t\t{name: '凤梨', value: 7},\n\t\t{name: '葡萄', value: 8},\n\t\t{name: '樱桃', value: 9},\n\t\t{name: '车厘子', value: 10},\n\t\t{name: '火龙果', value: 11},\n\t]\n})\n<\/script>\n")])])])],2),t._m(9),t._m(10),n("demo-block",[n("div",[n("p",[n("code",[t._v("render")]),t._v("后, 就会进行一次回调, 用于渲染第一次数据")])]),n("template",{slot:"source"},[n("element-demo4")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo5\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo5 = xmSelect.render({\n\tel: '#demo5', \n\tpaging: true,\n\tpageRemote: true,\n\tremoteMethod: function(val, cb, show, pageIndex){\n\t\t//val: 搜索框的内容, 不开启搜索默认为空, cb: 回调函数, show: 当前下拉框是否展开, pageIndex: 当前第几页\n\t\t\n\t\t//这里的axios类似于ajax\n\t\taxios({\n\t\t\tmethod: 'get',\n\t\t\turl: 'https://www.fastmock.site/mock/98228b1f16b7e5112d6c0c87921eabc1/xmSelect/search',\n\t\t\tparams: {\n\t\t\t\tkeyword: val + '_' + pageIndex,\n\t\t\t}\n\t\t}).then(response => {\n\t\t\t//这里是success的处理\n\t\t\tvar res = response.data;\n\t\t\t//回调需要两个参数, 第一个: 数据数组, 第二个: 总页码\n\t\t\tcb(res.data, 10)\n\t\t}).catch(err => {\n\t\t\t//这里是error的处理\n\t\t\tcb([], 0);\n\t\t});\n\t}\n})\n<\/script>\n")])])])],2),t._m(11),t._m(12),n("demo-block",[n("div",[n("p",[n("code",[t._v("render")]),t._v("后, 就会进行一次回调, 用于渲染第一次数据")])]),n("template",{slot:"source"},[n("element-demo5")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo6\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo6 = xmSelect.render({\n\tel: '#demo6', \n\t//配置搜索\n\tfilterable: true,\n\t//配置远程分页\n\tpaging: true,\n\tpageRemote: true,\n\t//数据处理\n\tremoteMethod: function(val, cb, show, pageIndex){\n\t\t//val: 搜索框的内容, 不开启搜索默认为空, cb: 回调函数, show: 当前下拉框是否展开, pageIndex: 当前第几页\n\t\t\n\t\t//这里的axios类似于ajax\n\t\taxios({\n\t\t\tmethod: 'get',\n\t\t\turl: 'https://www.fastmock.site/mock/98228b1f16b7e5112d6c0c87921eabc1/xmSelect/search',\n\t\t\tparams: {\n\t\t\t\tkeyword: val + '_' + pageIndex,\n\t\t\t}\n\t\t}).then(response => {\n\t\t\t//这里是success的处理\n\t\t\tvar res = response.data;\n\t\t\t//回调需要两个参数, 第一个: 数据数组, 第二个: 总页码\n\t\t\tcb(res.data, 10)\n\t\t}).catch(err => {\n\t\t\t//这里是error的处理\n\t\t\tcb([], 0);\n\t\t});\n\t}\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",paging:!0,data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3},{name:"赵六",value:4},{name:"苹果",value:5},{name:"香蕉",value:6},{name:"凤梨",value:7},{name:"葡萄",value:8},{name:"樱桃",value:9},{name:"车厘子",value:10},{name:"火龙果",value:11}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",paging:!0,pageSize:3,data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3},{name:"赵六",value:4},{name:"苹果",value:5},{name:"香蕉",value:6},{name:"凤梨",value:7},{name:"葡萄",value:8},{name:"樱桃",value:9},{name:"车厘子",value:10},{name:"火龙果",value:11}]})}))}}}(),"element-demo2":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo3"}}),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"demo3-5"}},[this._v("每页5条")]),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"demo3-10"}},[this._v("每页10条")]),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"demo3-20"}},[this._v("每页20条")])])}],mounted:function(){this.$nextTick((function(){for(var t=[],e=0;e<100;e++)t.push({name:"测试数据"+e,value:e});var n=xmSelect.render({el:"#demo3",paging:!0,pageSize:5,filterable:!0,data:t});document.getElementById("demo3-5").onclick=function(){n.update({pageSize:5})},document.getElementById("demo3-10").onclick=function(){n.update({pageSize:10})},document.getElementById("demo3-20").onclick=function(){n.update({pageSize:20})}}))}}}(),"element-demo3":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo4"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo4",paging:!0,pageSize:3,filterable:!0,pageEmptyShow:!1,data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3},{name:"赵六",value:4},{name:"苹果",value:5},{name:"香蕉",value:6},{name:"凤梨",value:7},{name:"葡萄",value:8},{name:"樱桃",value:9},{name:"车厘子",value:10},{name:"火龙果",value:11}]})}))}}}(),"element-demo4":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo5"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo5",paging:!0,pageRemote:!0,remoteMethod:function(t,e,n,a){axios({method:"get",url:"https://www.fastmock.site/mock/98228b1f16b7e5112d6c0c87921eabc1/xmSelect/search",params:{keyword:t+"_"+a}}).then((function(t){var n=t.data;e(n.data,10)})).catch((function(t){e([],0)}))}})}))}}}(),"element-demo5":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo6"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo6",filterable:!0,paging:!0,pageRemote:!0,remoteMethod:function(t,e,n,a){axios({method:"get",url:"https://www.fastmock.site/mock/98228b1f16b7e5112d6c0c87921eabc1/xmSelect/search",params:{keyword:t+"_"+a}}).then((function(t){var n=t.data;e(n.data,10)})).catch((function(t){e([],0)}))}})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"fen-ye"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#fen-ye"}},[this._v("¶")]),this._v(" 分页")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"qi-yong-fen-ye"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#qi-yong-fen-ye"}},[this._v("¶")]),this._v(" 启用分页")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("xmSelect.render({\n\t//...\n\tpaging: true,\n})\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"zi-ding-yi-tiao-shu"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zi-ding-yi-tiao-shu"}},[this._v("¶")]),this._v(" 自定义条数")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("xmSelect.render({\n\t//...\n\tpaging: true,\n\tpageSize: 3,\n})\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"sou-suo-fen-ye"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#sou-suo-fen-ye"}},[this._v("¶")]),this._v(" 搜索+分页")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("xmSelect.render({\n\t//...\n\tpaging: true,\n\tpageSize: 5,\n\tfilterable: true,\n})\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"sou-suo-fen-ye-wu-shu-ju-bu-zhan-shi-fen-ye"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#sou-suo-fen-ye-wu-shu-ju-bu-zhan-shi-fen-ye"}},[this._v("¶")]),this._v(" 搜索 + 分页 无数据 不展示分页")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("xmSelect.render({\n\t//...\n\tpaging: true,\n\tpageSize: 3,\n\tfilterable: true,\n\tpageEmptyShow: false,\n})\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"yuan-cheng-fen-ye"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#yuan-cheng-fen-ye"}},[this._v("¶")]),this._v(" 远程分页")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("xmSelect.render({\n    //...\n\t//开启分页\n    paging: true,\n\t//远程分页\n    pageRemote: true,\n\t//实现方法\n    remoteMethod: function(val, cb, show, pageIndex),\n})\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"sou-suo-yuan-cheng-fen-ye"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#sou-suo-yuan-cheng-fen-ye"}},[this._v("¶")]),this._v(" 搜索 + 远程分页")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("xmSelect.render({\n    //...\n\t//开启分页\n    paging: true,\n\t//远程分页\n    pageRemote: true,\n\t//实现方法\n    remoteMethod: function(val, cb, show, pageIndex),\n})\n")])])}],!1,null,null,null);r.options.__file="docs/mds/XM08.md";e.default=r.exports},705:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tradio: true,\n\ttoolbar: {show: true},\n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),this._m(2),e("demo-block",[e("template",{slot:"source"},[e("element-demo1")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\tradio: true,\n\tclickClose: true,\n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),this._m(3),e("demo-block",[e("template",{slot:"source"},[e("element-demo2")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo3\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo3 = xmSelect.render({\n\tel: '#demo3', \n\tradio: true,\n\tclickClose: true,\n\tmodel: {\n\t\tlabel: {\n\t\t\ttype: 'text',\n\t\t\ttext: {\n\t\t\t\t//左边拼接的字符\n\t\t\t\tleft: '',\n\t\t\t\t//右边拼接的字符\n\t\t\t\tright: '',\n\t\t\t\t//中间的分隔符\n\t\t\t\tseparator: ', ',\n\t\t\t},\n\t\t}\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",radio:!0,toolbar:{show:!0},data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",radio:!0,clickClose:!0,data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo2":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo3"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo3",radio:!0,clickClose:!0,model:{label:{type:"text",text:{left:"",right:"",separator:", "}}},data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"dan-xuan"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#dan-xuan"}},[this._v("¶")]),this._v(" 单选")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"kai-qi-dan-xuan"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#kai-qi-dan-xuan"}},[this._v("¶")]),this._v(" 开启单选")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"dan-xuan-wan-guan-bi-xia-la"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#dan-xuan-wan-guan-bi-xia-la"}},[this._v("¶")]),this._v(" 单选完关闭下拉")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"geng-huan-xian-shi-fang-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#geng-huan-xian-shi-fang-shi"}},[this._v("¶")]),this._v(" 更换显示方式")])}],!1,null,null,null);r.options.__file="docs/mds/XM09.md";e.default=r.exports},706:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),n("demo-block",[n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\trepeat: true,\n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(2),n("demo-block",[n("template",{slot:"source"},[n("element-demo1")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\trepeat: true,\n\tclickClose: true,\n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(3),n("demo-block",[n("div",[n("p",[t._v("好像这样只能增不能减了~~ 有待完善")])]),n("template",{slot:"source"},[n("element-demo2")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo3\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo3 = xmSelect.render({\n\tel: '#demo3', \n\trepeat: true,\n\tmodel: {\n\t\tlabel: {\n\t\t\ttype: 'count',\n\t\t\tcount: {\n\t\t\t\ttemplate: function(data, sels){\n\t\t\t\t\tvar res = {};\n\t\t\t\t\tsels.forEach(item => {\n\t\t\t\t\t   var name = item.name; \n\t\t\t\t\t   !res[name] && (res[name] = 0);\n\t\t\t\t\t   res[name] += 1;\n\t\t\t\t\t});\n\t\t\t\t\treturn Object.keys(res).map(key => {\n\t\t\t\t\t\treturn `${key} (${res[key]})`\n\t\t\t\t\t}).join(',');\n\t\t\t\t}\n\t\t\t},\n\t\t}\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",repeat:!0,data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",repeat:!0,clickClose:!0,data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo2":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo3"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo3",repeat:!0,model:{label:{type:"count",count:{template:function(t,e){var n={};return e.forEach((function(t){var e=t.name;!n[e]&&(n[e]=0),n[e]+=1})),Object.keys(n).map((function(t){return"".concat(t," (").concat(n[t],")")})).join(",")}}}},data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"chong-fu-xuan"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#chong-fu-xuan"}},[this._v("¶")]),this._v(" 重复选")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"kai-qi-chong-fu-xuan"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#kai-qi-chong-fu-xuan"}},[this._v("¶")]),this._v(" 开启重复选")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"chong-fu-xuan-wan-guan-bi-xia-la"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#chong-fu-xuan-wan-guan-bi-xia-la"}},[this._v("¶")]),this._v(" 重复选完关闭下拉")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"geng-huan-xian-shi-fang-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#geng-huan-xian-shi-fang-shi"}},[this._v("¶")]),this._v(" 更换显示方式")])}],!1,null,null,null);r.options.__file="docs/mds/XM10.md";e.default=r.exports},707:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),t._m(2),n("demo-block",[n("div",[n("p",[t._v("我的"),n("code",[t._v("name")]),t._v("是"),n("code",[t._v("label")]),t._v(", 我的"),n("code",[t._v("value")]),t._v("是"),n("code",[t._v("id")]),t._v(", 我有其他属性"),n("code",[t._v("group")])])]),n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n<button class=\"btn\" id=\"demo1-getValue\">获取选中值</button>\n<pre id=\"demo1-value\"></pre>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tprop: {\n\t\tname: 'label',\n\t\tvalue: 'id',\n\t},\n\tdata: [\n\t\t{label: '张三', id: 1, group: 1},\n\t\t{label: '李四', id: 2, group: 1},\n\t\t{label: '王五', id: 3, group: 2},\n\t]\n})\n\ndocument.getElementById('demo1-getValue').onclick = function(){\n\t//获取当前多选选中的值\n\tvar selectArr = demo1.getValue();\n\tdocument.getElementById('demo1-value').innerHTML = JSON.stringify(selectArr, null, 2);\n}\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}}),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"demo1-getValue"}},[this._v("获取选中值")]),this._v(" "),e("pre",{attrs:{id:"demo1-value"}})])}],mounted:function(){this.$nextTick((function(){var t=xmSelect.render({el:"#demo1",prop:{name:"label",value:"id"},data:[{label:"张三",id:1,group:1},{label:"李四",id:2,group:1},{label:"王五",id:3,group:2}]});document.getElementById("demo1-getValue").onclick=function(){var e=t.getValue();document.getElementById("demo1-value").innerHTML=JSON.stringify(e,null,2)}}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"zi-ding-yi-shu-xing"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zi-ding-yi-shu-xing"}},[this._v("¶")]),this._v(" 自定义属性")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"geng-huan-shu-xingkey"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#geng-huan-shu-xingkey"}},[this._v("¶")]),this._v(" 更换属性key")])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("p",[t._v("也许你的数据库返回的并不是"),n("code",[t._v("name")]),t._v("和"),n("code",[t._v("value")]),t._v(", 也许你提交的时候不止"),n("code",[t._v("name")]),t._v("和"),n("code",[t._v("value")]),t._v(", 怎么办? 自定义就行")])}],!1,null,null,null);r.options.__file="docs/mds/XM11.md";e.default=r.exports},708:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),n("demo-block",[n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(2),n("demo-block",[n("template",{slot:"source"},[n("element-demo1")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\ttheme: {\n\t\tcolor: '#e54d42',\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(3),n("demo-block",[n("template",{slot:"source"},[n("element-demo2")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo3\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo3 = xmSelect.render({\n\tel: '#demo3', \n\ttheme: {\n\t\tcolor: '#f37b1d',\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(4),n("demo-block",[n("template",{slot:"source"},[n("element-demo3")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo4\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo4 = xmSelect.render({\n\tel: '#demo4', \n\ttheme: {\n\t\tcolor: '#fbbd08',\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(5),n("demo-block",[n("template",{slot:"source"},[n("element-demo4")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo5\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo5 = xmSelect.render({\n\tel: '#demo5', \n\ttheme: {\n\t\tcolor: '#8dc63f',\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(6),n("demo-block",[n("template",{slot:"source"},[n("element-demo5")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo6\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo6 = xmSelect.render({\n\tel: '#demo6', \n\ttheme: {\n\t\tcolor: '#1cbbb4',\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(7),n("demo-block",[n("template",{slot:"source"},[n("element-demo6")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo7\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo7 = xmSelect.render({\n\tel: '#demo7', \n\ttheme: {\n\t\tcolor: '#0081ff',\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(8),n("demo-block",[n("template",{slot:"source"},[n("element-demo7")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo8\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo8 = xmSelect.render({\n\tel: '#demo8', \n\ttheme: {\n\t\tcolor: '#6739b6',\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(9),n("demo-block",[n("template",{slot:"source"},[n("element-demo8")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo9\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo9 = xmSelect.render({\n\tel: '#demo9', \n\ttheme: {\n\t\tcolor: '#9c26b0',\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(10),n("demo-block",[n("template",{slot:"source"},[n("element-demo9")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo10\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo10 = xmSelect.render({\n\tel: '#demo10', \n\ttheme: {\n\t\tcolor: '#e03997',\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(11),n("demo-block",[n("template",{slot:"source"},[n("element-demo10")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo11\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo11 = xmSelect.render({\n\tel: '#demo11', \n\ttheme: {\n\t\tcolor: '#a5673f',\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(12),n("demo-block",[n("template",{slot:"source"},[n("element-demo11")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo12\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo12 = xmSelect.render({\n\tel: '#demo12', \n\ttheme: {\n\t\tcolor: '#8799a3',\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(13),n("demo-block",[n("template",{slot:"source"},[n("element-demo12")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo13\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo13 = xmSelect.render({\n\tel: '#demo13', \n\ttheme: {\n\t\tcolor: '#aaaaaa',\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(14),n("demo-block",[n("template",{slot:"source"},[n("element-demo13")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo14\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo14 = xmSelect.render({\n\tel: '#demo14', \n\ttheme: {\n\t\tcolor: '#333333',\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(15)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",theme:{color:"#e54d42"},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo2":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo3"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo3",theme:{color:"#f37b1d"},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo3":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo4"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo4",theme:{color:"#fbbd08"},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo4":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo5"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo5",theme:{color:"#8dc63f"},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo5":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo6"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo6",theme:{color:"#1cbbb4"},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo6":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo7"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo7",theme:{color:"#0081ff"},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo7":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo8"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo8",theme:{color:"#6739b6"},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo8":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo9"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo9",theme:{color:"#9c26b0"},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo9":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo10"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo10",theme:{color:"#e03997"},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo10":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo11"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo11",theme:{color:"#a5673f"},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo11":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo12"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo12",theme:{color:"#8799a3"},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo12":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo13"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo13",theme:{color:"#aaaaaa"},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo13":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo14"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo14",theme:{color:"#333333"},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"zhu-ti"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zhu-ti"}},[this._v("¶")]),this._v(" 主题")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"jing-dian-lu-009688"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#jing-dian-lu-009688"}},[this._v("¶")]),this._v(" 经典绿 ( #009688 )")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"yan-hong-e54d42"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#yan-hong-e54d42"}},[this._v("¶")]),this._v(" 嫣红 ( #e54d42 )")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"jie-cheng-f37b1d"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#jie-cheng-f37b1d"}},[this._v("¶")]),this._v(" 桔橙 ( #f37b1d )")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"ming-huang-fbbd08"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#ming-huang-fbbd08"}},[this._v("¶")]),this._v(" 明黄 ( #fbbd08 )")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"gan-lan-8dc63f"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#gan-lan-8dc63f"}},[this._v("¶")]),this._v(" 橄榄 ( #8dc63f )")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"tian-qing-1cbbb4"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#tian-qing-1cbbb4"}},[this._v("¶")]),this._v(" 天青 ( #1cbbb4 )")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"hai-lan-0081ff"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#hai-lan-0081ff"}},[this._v("¶")]),this._v(" 海蓝 ( #0081ff )")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"cha-zi-6739b6"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#cha-zi-6739b6"}},[this._v("¶")]),this._v(" 姹紫 ( #6739b6 )")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"mu-jin-9c26b0"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#mu-jin-9c26b0"}},[this._v("¶")]),this._v(" 木槿 ( #9c26b0 )")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"tao-fen-e03997"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#tao-fen-e03997"}},[this._v("¶")]),this._v(" 桃粉 ( #e03997 )")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"zong-he-a5673f"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zong-he-a5673f"}},[this._v("¶")]),this._v(" 棕褐 ( #a5673f )")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"xuan-hui-8799a3"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xuan-hui-8799a3"}},[this._v("¶")]),this._v(" 玄灰 ( #8799a3 )")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"cao-hui-aaaaaa"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#cao-hui-aaaaaa"}},[this._v("¶")]),this._v(" 草灰 ( #aaaaaa )")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"mo-hei-333333"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#mo-hei-333333"}},[this._v("¶")]),this._v(" 墨黑 ( #333333 )")])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"warning"},[e("p",[this._v("颜色值来源于"),e("a",{attrs:{href:"https://github.com/weilanwl/ColorUI"}},[this._v("ColorUI")]),this._v(", 有兴趣的可以看看")])])}],!1,null,null,null);r.options.__file="docs/mds/XM12.md";e.default=r.exports},709:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n<button class=\"btn\" id=\"demo1-open\">打开下拉框</button>\n<button class=\"btn\" id=\"demo1-close\">关闭下拉框</button>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n\n\ndocument.getElementById('demo1-open').onclick = function(){\n\t//这里延迟1S, 是因为, 点击下拉框外边的位置 会出发关闭事件, 所以延迟演示效果\n\tsetTimeout(function(){\n\t\tdemo1.opened();\n\t}, 1000);\n}\n\ndocument.getElementById('demo1-close').onclick = function(){\n\t//先点一下关闭, 然后把下拉框点开, 3S后会自动关闭\n\tsetTimeout(function(){\n\t\tdemo1.closed();\n\t}, 3000);\n}\n<\/script>\n")])])])],2),this._m(2),e("demo-block",[e("template",{slot:"source"},[e("element-demo1")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t],\n\tshow(){\n\t\talert('打开了')\n\t},\n\thide(){\n\t\talert('关闭了')\n\t}\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}}),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"demo1-open"}},[this._v("打开下拉框")]),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"demo1-close"}},[this._v("关闭下拉框")])])}],mounted:function(){this.$nextTick((function(){var t=xmSelect.render({el:"#demo1",data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}]});document.getElementById("demo1-open").onclick=function(){setTimeout((function(){t.opened()}),1e3)},document.getElementById("demo1-close").onclick=function(){setTimeout((function(){t.closed()}),3e3)}}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}],show:function(){alert("打开了")},hide:function(){alert("关闭了")}})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"xian-shi-yu-yin-cang"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xian-shi-yu-yin-cang"}},[this._v("¶")]),this._v(" 显示与隐藏")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"zhu-dong-da-kai-guan-bi-xia-la-kuang"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zhu-dong-da-kai-guan-bi-xia-la-kuang"}},[this._v("¶")]),this._v(" 主动打开/关闭下拉框")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"jian-ting-da-kai-guan-bi-xia-la-kuang"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#jian-ting-da-kai-guan-bi-xia-la-kuang"}},[this._v("¶")]),this._v(" 监听打开/关闭下拉框")])}],!1,null,null,null);r.options.__file="docs/mds/XM13.md";e.default=r.exports},710:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),n("demo-block",[n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2, selected: true},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(2),t._m(3),n("demo-block",[n("template",{slot:"source"},[n("element-demo1")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\tmodel: {\n\t\tlabel: {\n\t\t\ttype: 'block',\n\t\t\tblock: {\n\t\t\t\t//最大显示数量, 0:不限制\n\t\t\t\tshowCount: 0,\n\t\t\t\t//是否显示删除图标\n\t\t\t\tshowIcon: false,\n\t\t\t}\n\t\t}\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2, selected: true},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(4),t._m(5),n("demo-block",[n("template",{slot:"source"},[n("element-demo2")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo3\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo3 = xmSelect.render({\n\tel: '#demo3', \n\tmodel: {\n\t\tlabel: {\n\t\t\ttype: 'block',\n\t\t\tblock: {\n\t\t\t\t//最大显示数量, 0:不限制\n\t\t\t\tshowCount: 1,\n\t\t\t\t//是否显示删除图标\n\t\t\t\tshowIcon: true,\n\t\t\t}\n\t\t}\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2, selected: true},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(6),t._m(7),n("demo-block",[n("template",{slot:"source"},[n("element-demo3")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo4\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo4 = xmSelect.render({\n\tel: '#demo4', \n\tmodel: {\n\t\tlabel: {\n\t\t\ttype: 'text',\n\t\t\t//使用字符串拼接的方式\n\t\t\ttext: {\n\t\t\t\t//左边拼接的字符\n\t\t\t\tleft: '【',\n\t\t\t\t//右边拼接的字符\n\t\t\t\tright: '】',\n\t\t\t\t//中间的分隔符\n\t\t\t\tseparator: '，',\n\t\t\t},\n\t\t}\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2, selected: true},\n\t\t{name: '王五', value: 3},\n\t],\n})\n<\/script>\n")])])])],2),t._m(8),t._m(9),n("demo-block",[n("template",{slot:"source"},[n("element-demo4")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo5\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo5 = xmSelect.render({\n\tel: '#demo5', \n\tmodel: {\n\t\tlabel: {\n\t\t\ttype: 'xxxx', //自定义与下面的对应\n\t\t\txxxx: {\n\t\t\t\ttemplate(data, sels){\n\t\t\t\t\treturn \"已选中 \" + sels.length + \" 项, 共 \" + data.length + \" 项\"\n\t\t\t\t}\n\t\t\t},\n\t\t}\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2, selected: true},\n\t\t{name: '王五', value: 3},\n\t],\n})\n<\/script>\n")])])])],2),t._m(10),t._m(11),n("demo-block",[n("template",{slot:"source"},[n("element-demo5")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo6\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo6 = xmSelect.render({\n\tel: '#demo6', \n\tmodel: {\n\t\tlabel: {\n\t\t\ttype: 'xxxx', //自定义与下面的对应\n\t\t\txxxx: {\n\t\t\t\ttemplate(data, sels){\n\t\t\t\t\t//也可以是html\n\t\t\t\t\treturn `<div style=\"color: red;\">${sels.length} / ${data.length}</div>`\n\t\t\t\t}\n\t\t\t},\n\t\t}\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2, selected: true},\n\t\t{name: '王五', value: 3},\n\t],\n})\n<\/script>\n")])])])],2),t._m(12),t._m(13),n("demo-block",[n("template",{slot:"source"},[n("element-demo6")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo7\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo7 = xmSelect.render({\n\tel: '#demo7', \n\tmodel: {\n\t\ttype: 'relative',\n\t},\n\tfilterable: true,\n\ttoolbar: { show: true },\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2, selected: true},\n\t\t{name: '王五', value: 3},\n\t],\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2,selected:!0},{name:"王五",value:3}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",model:{label:{type:"block",block:{showCount:0,showIcon:!1}}},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2,selected:!0},{name:"王五",value:3}]})}))}}}(),"element-demo2":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo3"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo3",model:{label:{type:"block",block:{showCount:1,showIcon:!0}}},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2,selected:!0},{name:"王五",value:3}]})}))}}}(),"element-demo3":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo4"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo4",model:{label:{type:"text",text:{left:"【",right:"】",separator:"，"}}},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2,selected:!0},{name:"王五",value:3}]})}))}}}(),"element-demo4":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo5"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo5",model:{label:{type:"xxxx",xxxx:{template:function(t,e){return"已选中 "+e.length+" 项, 共 "+t.length+" 项"}}}},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2,selected:!0},{name:"王五",value:3}]})}))}}}(),"element-demo5":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo6"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo6",model:{label:{type:"xxxx",xxxx:{template:function(t,e){return'<div style="color: red;">'.concat(e.length," / ").concat(t.length,"</div>")}}}},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2,selected:!0},{name:"王五",value:3}]})}))}}}(),"element-demo6":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo7"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo7",model:{type:"relative"},filterable:!0,toolbar:{show:!0},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2,selected:!0},{name:"王五",value:3}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"xian-shi-fang-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xian-shi-fang-shi"}},[this._v("¶")]),this._v(" 显示方式")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"fang-kuai-xing-zhuang"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#fang-kuai-xing-zhuang"}},[this._v("¶")]),this._v(" 方块形状")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"fang-kuai-xing-zhuang-yin-cang-shan-chu-tu-biao"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#fang-kuai-xing-zhuang-yin-cang-shan-chu-tu-biao"}},[this._v("¶")]),this._v(" 方块形状, 隐藏删除图标")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("model: {\n\tlabel: {\n\t\ttype: 'block',\n\t\tblock: {\n\t\t\t//最大显示数量, 0:不限制\n\t\t\tshowCount: 0,\n\t\t\t//是否显示删除图标\n\t\t\tshowIcon: false,\n\t\t}\n\t}\n},\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"fang-kuai-xing-zhuang-chao-guo1-ge-yin-cang"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#fang-kuai-xing-zhuang-chao-guo1-ge-yin-cang"}},[this._v("¶")]),this._v(" 方块形状, 超过1个隐藏")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("model: {\n\tlabel: {\n\t\ttype: 'block',\n\t\tblock: {\n\t\t\t//最大显示数量, 0:不限制\n\t\t\tshowCount: 1,\n\t\t\t//是否显示删除图标\n\t\t\tshowIcon: true,\n\t\t}\n\t}\n},\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"jian-dan-pin-jie-xing-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#jian-dan-pin-jie-xing-shi"}},[this._v("¶")]),this._v(" 简单拼接形式")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("model: {\n\tlabel: {\n\t\ttype: 'text',\n\t\t//使用字符串拼接的方式\n\t\ttext: {\n\t\t\t//左边拼接的字符\n\t\t\tleft: '【',\n\t\t\t//右边拼接的字符\n\t\t\tright: '】',\n\t\t\t//中间的分隔符\n\t\t\tseparator: '，',\n\t\t},\n\t}\n},\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"zi-ding-yi-xian-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zi-ding-yi-xian-shi"}},[this._v("¶")]),this._v(" 自定义显示")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v('model: {\n\tlabel: {\n\t\ttype: \'xxxx\', //自定义与下面的对应\n\t\txxxx: {\n\t\t\ttemplate(data, sels){\n\t\t\t\treturn "已选中 " + sels.length + " 项, 共 " + data.length + " 项"\n\t\t\t}\n\t\t},\n\t}\n},\n')])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"zi-ding-yi-xian-shihtml"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zi-ding-yi-xian-shihtml"}},[this._v("¶")]),this._v(" 自定义显示HTML")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("model: {\n\tlabel: {\n\t\ttype: 'xxxx', //自定义与下面的对应\n\t\txxxx: {\n\t\t\ttemplate(data, sels){\n\t\t\t\t//也可以是html\n\t\t\t\treturn `<div style=\"color: red;\">${sels.length} / ${data.length}</div>`\n\t\t\t}\n\t\t},\n\t}\n},\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"zhi-jie-xian-shi-xia-la"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zhi-jie-xian-shi-xia-la"}},[this._v("¶")]),this._v(" 直接显示下拉")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("model: {\n\ttype: 'relative', //默认 absolute\n},\n")])])}],!1,null,null,null);r.options.__file="docs/mds/XM14.md";e.default=r.exports},711:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\ttemplate({ item, sels, name, value }){\n\t\treturn item.name  + '<span style=\"position: absolute; right: 10px; color: #8799a3\">'+value+'</span>' \n\t},\n\tprop: {\n\t\tname: 'showname',\n\t},\n\tdata: [\n\t\t{name: '张三', value: 'zhangsan', showname: '组织部-张三', selected: true},\n\t\t{name: '李四', value: 'lisi', showname: '策划部-李四', selected: true},\n\t\t{name: '王五', value: 'wangwu', showname: '运营部-王五' },\n\t]\n})\n<\/script>\n")])])])],2),this._m(2),e("demo-block",[e("template",{slot:"source"},[e("element-demo1")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar icons = ['layui-icon layui-icon-face-smile', 'layui-icon layui-icon-cellphone']\n\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\tmodel: {\n\t\tlabel: {\n\t\t\tblock: {\n\t\t\t\ttemplate: function(item, sels){\n\t\t\t\t\treturn '<i class=\"'+icons[item.group]+'\"></i>' + item.name;\n\t\t\t\t},\n\t\t\t},\n\t\t}\n\t},\n\ttemplate({ item, sels, name, value }){\n\t\treturn item.name  + '<span style=\"position: absolute; right: 10px; color: #8799a3\">'+value+'</span>' \n\t},\n\tdata: [\n\t\t{group: 1, name: '张三', value: 'zhangsan', selected: true},\n\t\t{group: 0, name: '李四', value: 'lisi', selected: true},\n\t\t{group: 1, name: '王五', value: 'wangwu' },\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",template:function(t){var e=t.item,n=(t.sels,t.name,t.value);return e.name+'<span style="position: absolute; right: 10px; color: #8799a3">'+n+"</span>"},prop:{name:"showname"},data:[{name:"张三",value:"zhangsan",showname:"组织部-张三",selected:!0},{name:"李四",value:"lisi",showname:"策划部-李四",selected:!0},{name:"王五",value:"wangwu",showname:"运营部-王五"}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){var t=["layui-icon layui-icon-face-smile","layui-icon layui-icon-cellphone"];xmSelect.render({el:"#demo2",model:{label:{block:{template:function(e,n){return'<i class="'+t[e.group]+'"></i>'+e.name}}}},template:function(t){var e=t.item,n=(t.sels,t.name,t.value);return e.name+'<span style="position: absolute; right: 10px; color: #8799a3">'+n+"</span>"},data:[{group:1,name:"张三",value:"zhangsan",selected:!0},{group:0,name:"李四",value:"lisi",selected:!0},{group:1,name:"王五",value:"wangwu"}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"gou-jian-xuan-xiang"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#gou-jian-xuan-xiang"}},[this._v("¶")]),this._v(" 构建选项")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"zi-ding-yi-xuan-ran"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zi-ding-yi-xuan-ran"}},[this._v("¶")]),this._v(" 自定义渲染")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"zi-ding-yi-xuan-ran---label"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zi-ding-yi-xuan-ran---label"}},[this._v("¶")]),this._v(" 自定义渲染 - label")])}],!1,null,null,null);r.options.__file="docs/mds/XM15.md";e.default=r.exports},712:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),t._m(2),n("p",[t._v("实时监听多选的选中状态变化")]),n("demo-block",[n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<br/><br/>\n<button class=\"btn\" id=\"setValue1\">监听setValue(['zhangsan'], null, true)</button>\n<button class=\"btn\" id=\"setValue2\">不监听setValue(['zhangsan'])</button>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\ton: function(data){\n\t\t//arr:  当前多选已选中的数据\n\t\tvar arr = data.arr;\n\t\t//change, 此次选择变化的数据,数组\n\t\tvar change = data.change;\n\t\t//isAdd, 此次操作是新增还是删除\n\t\tvar isAdd = data.isAdd;\n\t\t\n\t\talert('已有: '+arr.length+' 变化: '+change.length+', 状态: ' + isAdd)\n\t},\n\tdata: [\n\t\t{name: '张三', value: 'zhangsan', selected: true},\n\t\t{name: '李四', value: 'lisi', selected: true},\n\t\t{name: '王五', value: 'wangwu'},\n\t]\n})\n\ndocument.getElementById('setValue1').onclick = function(){\n\tdemo1.setValue(['zhangsan'], null, true);\n}\n\ndocument.getElementById('setValue2').onclick = function(){\n\tdemo1.setValue(['zhangsan']);\n}\n<\/script>\n")])])])],2),t._m(3),n("p",[t._v("选中北京后, 不能选中上海, 二者互斥")]),n("demo-block",[n("template",{slot:"source"},[n("element-demo1")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\ttoolbar: {\n\t\tshow: true\n\t},\n\ton: function(data){\n\t\t//arr:  当前多选已选中的数据\n\t\tvar arr = data.arr;\n\t\t//change, 此次选择变化的数据,数组\n\t\tvar change = data.change;\n\t\t//isAdd, 此次操作是新增还是删除\n\t\tvar isAdd = data.isAdd;\n\t\t\n\t\tconsole.log(arr);\n\t\t\n\t\tif(isAdd){\n\t\t\tvar item = change[0];\n\t\t\tvar index = arr.findIndex(i => i.mutex == item.mutex && i.value != item.value);\n\t\t\tif(index != -1){\n\t\t\t\tarr.splice(index, 1);\n\t\t\t}\n\t\t}\n\t},\n\tdata: [\n\t\t{name: '北京', value: 1, mutex: 1, selected: true},\n\t\t{name: '上海', value: 2, mutex: 1},\n\t\t{name: '广州', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),t._m(4),n("p",[t._v("有时全选也是一种状态, 列表中有全选, 与其他选项互斥")]),n("demo-block",[n("template",{slot:"source"},[n("element-demo2")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo3\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo3 = xmSelect.render({\n\tel: '#demo3', \n\ton: function(data){\n\t\t//可以return一个数组, 代表想选中的数据\n\t\t\n\t\t//arr:  当前多选已选中的数据\n\t\tvar arr = data.arr;\n\t\t//change, 此次选择变化的数据,数组\n\t\tvar change = data.change;\n\t\t//isAdd, 此次操作是新增还是删除\n\t\tvar isAdd = data.isAdd;\n\t\t\n\t\tif(isAdd){\n\t\t\tvar allItem = change.find(function(item){\n\t\t\t\treturn item.value === 0;\n\t\t\t})\n\t\t\tif(allItem){\n\t\t\t\treturn [allItem];\n\t\t\t}\n\t\t\tallItem = arr.find(function(item){\n\t\t\t\treturn item.value === 0;\n\t\t\t})\n\t\t\tif(allItem){\n\t\t\t\treturn change;\n\t\t\t}\n\t\t}\n\t\t\n\t},\n\tdata: [\n\t\t{name: '全选', value: 0},\n\t\t{name: '北京', value: 1},\n\t\t{name: '上海', value: 2},\n\t\t{name: '广州', value: 3},\n\t\t{name: '深圳', value: 4},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}}),this._v(" "),e("br"),e("br"),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"setValue1"}},[this._v("监听setValue(['zhangsan'], null, true)")]),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"setValue2"}},[this._v("不监听setValue(['zhangsan'])")])])}],mounted:function(){this.$nextTick((function(){var t=xmSelect.render({el:"#demo1",on:function(t){var e=t.arr,n=t.change,a=t.isAdd;alert("已有: "+e.length+" 变化: "+n.length+", 状态: "+a)},data:[{name:"张三",value:"zhangsan",selected:!0},{name:"李四",value:"lisi",selected:!0},{name:"王五",value:"wangwu"}]});document.getElementById("setValue1").onclick=function(){t.setValue(["zhangsan"],null,!0)},document.getElementById("setValue2").onclick=function(){t.setValue(["zhangsan"])}}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",toolbar:{show:!0},on:function(t){var e=t.arr,n=t.change,a=t.isAdd;if(console.log(e),a){var i=n[0],s=e.findIndex((function(t){return t.mutex==i.mutex&&t.value!=i.value}));-1!=s&&e.splice(s,1)}},data:[{name:"北京",value:1,mutex:1,selected:!0},{name:"上海",value:2,mutex:1},{name:"广州",value:3}]})}))}}}(),"element-demo2":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo3"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo3",on:function(t){var e=t.arr,n=t.change;if(t.isAdd){var a=n.find((function(t){return 0===t.value}));if(a)return[a];if(a=e.find((function(t){return 0===t.value})))return n}},data:[{name:"全选",value:0},{name:"北京",value:1},{name:"上海",value:2},{name:"广州",value:3},{name:"深圳",value:4}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"jian-ting-xuan-ze"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#jian-ting-xuan-ze"}},[this._v("¶")]),this._v(" 监听选择")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("xmSelect.render({\n\ton: function(data){\n\t\t//arr:  当前多选已选中的数据\n\t\tvar arr = data.arr;\n\t\t//change, 此次选择变化的数据,数组\n\t\tvar change = data.change;\n\t\t//isAdd, 此次操作是新增还是删除\n\t\tvar isAdd = data.isAdd;\n\t\t\n\t\t//可以return一个数组, 代表想选中的数据\n\t\t//return []\n\t},\n})\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"shi-shi-jian-ting"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#shi-shi-jian-ting"}},[this._v("¶")]),this._v(" 实时监听")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"jian-ting-dong-tai-fu-zhi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#jian-ting-dong-tai-fu-zhi"}},[this._v("¶")]),this._v(" 监听+动态赋值")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"jian-ting-dong-tai-xiu-gai"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#jian-ting-dong-tai-xiu-gai"}},[this._v("¶")]),this._v(" 监听+动态修改")])}],!1,null,null,null);r.options.__file="docs/mds/XM16.md";e.default=r.exports},713:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),e("demo-block",[e("div",[e("p",[this._v("事实证明分页是好使的 ^_^")])]),e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v('<div id="demo1" class="xm-select-demo"></div>\n<button class="btn" id="demo1-test1">测试1000条</button>\n<button class="btn" id="demo1-test2">测试10000条</button>\n<button class="btn" id="demo1-test3">测试10000条+分页</button>\n<pre id="demo1-result"></pre>\n\n<script>\nfunction run(count, paging){\n\t//生成数据\n\tvar data = [];\n\tfor(var i = 0; i < count; i++){\n\t\tdata.push({\n\t\t\tname: \'测试数据\' + i,\n\t\t\tvalue: i,\n\t\t})\n\t}\n\t\n\t//记录开始渲染时间\n\tvar startTime = Date.now();\n\t\n\tvar demo1 = xmSelect.render({\n\t\tel: \'#demo1\', \n\t\tpaging,\n\t\tdata\n\t})\n\t\n\t//记录结束时间\n\tvar endTime = Date.now();\n\t\n\tdocument.getElementById(\'demo1-result\').innerText = `渲染耗时: ${endTime - startTime} ms`\n}\n\ndocument.getElementById(\'demo1-test1\').onclick = function(){\n\trun(1000, false)\n};\n\ndocument.getElementById(\'demo1-test2\').onclick = function(){\n\trun(10000, false)\n};\n\ndocument.getElementById(\'demo1-test3\').onclick = function(){\n\trun(10000, true)\n};\n\nrun(1000, false);\n\n<\/script>\n')])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}}),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"demo1-test1"}},[this._v("测试1000条")]),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"demo1-test2"}},[this._v("测试10000条")]),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"demo1-test3"}},[this._v("测试10000条+分页")]),this._v(" "),e("pre",{attrs:{id:"demo1-result"}})])}],mounted:function(){this.$nextTick((function(){function t(t,e){for(var n=[],a=0;a<t;a++)n.push({name:"测试数据"+a,value:a});var i=Date.now(),s=(xmSelect.render({el:"#demo1",paging:e,data:n}),Date.now());document.getElementById("demo1-result").innerText="渲染耗时: ".concat(s-i," ms")}document.getElementById("demo1-test1").onclick=function(){t(1e3,!1)},document.getElementById("demo1-test2").onclick=function(){t(1e4,!1)},document.getElementById("demo1-test3").onclick=function(){t(1e4,!0)},t(1e3,!1)}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"xing-neng-ce-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xing-neng-ce-shi"}},[this._v("¶")]),this._v(" 性能测试")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"shu-ju-xuan-ran-hao-shi-ce-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#shu-ju-xuan-ran-hao-shi-ce-shi"}},[this._v("¶")]),this._v(" 数据渲染耗时测试")])}],!1,null,null,null);r.options.__file="docs/mds/XM17.md";e.default=r.exports},714:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tmax: 2,\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2, selected: true},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),this._m(2),e("demo-block",[e("template",{slot:"source"},[e("element-demo1")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\tmax: 2,\n\tmaxMethod(seles, item){\n\t\talert(`${item.name}不能选了, 已经超了`)\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2, selected: true},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2),this._m(3),e("demo-block",[e("template",{slot:"source"},[e("element-demo2")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo3\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo3 = xmSelect.render({\n\tel: '#demo3', \n\tmax: 2,\n\ttheme: {\n\t\tmaxColor: 'orange',\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2, selected: true},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",max:2,data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2,selected:!0},{name:"王五",value:3}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",max:2,maxMethod:function(t,e){alert("".concat(e.name,"不能选了, 已经超了"))},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2,selected:!0},{name:"王五",value:3}]})}))}}}(),"element-demo2":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo3"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo3",max:2,theme:{maxColor:"orange"},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2,selected:!0},{name:"王五",value:3}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"duo-xuan-shang-xian"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#duo-xuan-shang-xian"}},[this._v("¶")]),this._v(" 多选上限")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"zui-duo-xuan-ze2-ge"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zui-duo-xuan-ze2-ge"}},[this._v("¶")]),this._v(" 最多选择2个")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"zi-ding-yi-xuan-chao-de-ti-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zi-ding-yi-xuan-chao-de-ti-shi"}},[this._v("¶")]),this._v(" 自定义选超的提示")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"zi-ding-yi-xuan-chao-de-shan-shuo-yan-se"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zi-ding-yi-xuan-chao-de-shan-shuo-yan-se"}},[this._v("¶")]),this._v(" 自定义选超的闪烁颜色")])}],!1,null,null,null);r.options.__file="docs/mds/XM18.md";e.default=r.exports},715:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),n("demo-block",[n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\ttoolbar: {\n\t\tshow: true,\n\t},\n\tfilterable: true,\n\tpaging: true,\n\tpageSize: 3,\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2, selected: true},\n\t\t{name: '王五', value: 3},\n\t\t{name: '赵六', value: 4},\n\t\t{name: '苹果', value: 5},\n\t\t{name: '香蕉', value: 6},\n\t\t{name: '凤梨', value: 7},\n\t\t{name: '葡萄', value: 8},\n\t\t{name: '樱桃', value: 9},\n\t\t{name: '车厘子', value: 10},\n\t\t{name: '火龙果', value: 11},\n\t]\n})\n<\/script>\n")])])])],2),t._m(2),n("demo-block",[n("template",{slot:"source"},[n("element-demo1")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\ttoolbar: {\n\t\tshow: true,\n\t\tshowIcon: false,\n\t},\n\tfilterable: true,\n\tpaging: true,\n\tpageSize: 3,\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2, selected: true},\n\t\t{name: '王五', value: 3},\n\t\t{name: '赵六', value: 4},\n\t\t{name: '苹果', value: 5},\n\t\t{name: '香蕉', value: 6},\n\t\t{name: '凤梨', value: 7},\n\t\t{name: '葡萄', value: 8},\n\t\t{name: '樱桃', value: 9},\n\t\t{name: '车厘子', value: 10},\n\t\t{name: '火龙果', value: 11},\n\t]\n})\n<\/script>\n")])])])],2),t._m(3),n("demo-block",[n("template",{slot:"source"},[n("element-demo2")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo3\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo3 = xmSelect.render({\n\tel: '#demo3', \n\ttoolbar: {\n\t\tshow: true,\n\t\tlist: ['ALL', {\n\t\t\tname: '自定义',\n\t\t\ticon: 'el-icon-star-off',\n\t\t\tmethod(data){\n\t\t\t\talert('我是自定义的');\n\t\t\t}\n\t\t}]\n\t},\n\tfilterable: true,\n\tpaging: true,\n\tpageSize: 3,\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2, selected: true},\n\t\t{name: '王五', value: 3},\n\t\t{name: '赵六', value: 4},\n\t\t{name: '苹果', value: 5},\n\t\t{name: '香蕉', value: 6},\n\t\t{name: '凤梨', value: 7},\n\t\t{name: '葡萄', value: 8},\n\t\t{name: '樱桃', value: 9},\n\t\t{name: '车厘子', value: 10},\n\t\t{name: '火龙果', value: 11},\n\t]\n})\n<\/script>\n")])])])],2),t._m(4),t._m(5),n("demo-block",[n("template",{slot:"source"},[n("element-demo3")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo4\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo4 = xmSelect.render({\n\tel: '#demo4', \n\ttoolbar: {\n\t\tshow: true,\n\t\tlist: [ 'ALL', 'CLEAR', 'REVERSE' ]\n\t},\n\tfilterable: true,\n\tpaging: true,\n\tpageSize: 3,\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2, selected: true},\n\t\t{name: '王五', value: 3},\n\t\t{name: '赵六', value: 4},\n\t\t{name: '苹果', value: 5},\n\t\t{name: '香蕉', value: 6},\n\t\t{name: '凤梨', value: 7},\n\t\t{name: '葡萄', value: 8},\n\t\t{name: '樱桃', value: 9},\n\t\t{name: '车厘子', value: 10},\n\t\t{name: '火龙果', value: 11},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",toolbar:{show:!0},filterable:!0,paging:!0,pageSize:3,data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2,selected:!0},{name:"王五",value:3},{name:"赵六",value:4},{name:"苹果",value:5},{name:"香蕉",value:6},{name:"凤梨",value:7},{name:"葡萄",value:8},{name:"樱桃",value:9},{name:"车厘子",value:10},{name:"火龙果",value:11}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",toolbar:{show:!0,showIcon:!1},filterable:!0,paging:!0,pageSize:3,data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2,selected:!0},{name:"王五",value:3},{name:"赵六",value:4},{name:"苹果",value:5},{name:"香蕉",value:6},{name:"凤梨",value:7},{name:"葡萄",value:8},{name:"樱桃",value:9},{name:"车厘子",value:10},{name:"火龙果",value:11}]})}))}}}(),"element-demo2":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo3"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo3",toolbar:{show:!0,list:["ALL",{name:"自定义",icon:"el-icon-star-off",method:function(t){alert("我是自定义的")}}]},filterable:!0,paging:!0,pageSize:3,data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2,selected:!0},{name:"王五",value:3},{name:"赵六",value:4},{name:"苹果",value:5},{name:"香蕉",value:6},{name:"凤梨",value:7},{name:"葡萄",value:8},{name:"樱桃",value:9},{name:"车厘子",value:10},{name:"火龙果",value:11}]})}))}}}(),"element-demo3":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo4"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo4",toolbar:{show:!0,list:["ALL","CLEAR","REVERSE"]},filterable:!0,paging:!0,pageSize:3,data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2,selected:!0},{name:"王五",value:3},{name:"赵六",value:4},{name:"苹果",value:5},{name:"香蕉",value:6},{name:"凤梨",value:7},{name:"葡萄",value:8},{name:"樱桃",value:9},{name:"车厘子",value:10},{name:"火龙果",value:11}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"gong-ju-tiao"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#gong-ju-tiao"}},[this._v("¶")]),this._v(" 工具条")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"mo-ren-gong-ju-tiao"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#mo-ren-gong-ju-tiao"}},[this._v("¶")]),this._v(" 默认工具条")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"yin-cang-tu-biao"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#yin-cang-tu-biao"}},[this._v("¶")]),this._v(" 隐藏图标")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"zi-ding-yi-gong-ju-tiao"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zi-ding-yi-gong-ju-tiao"}},[this._v("¶")]),this._v(" 自定义工具条")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"quan-bu-gong-ju-tiao"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#quan-bu-gong-ju-tiao"}},[this._v("¶")]),this._v(" 全部工具条")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("//全选, 清空, 反选\nxmSelect.render({\n\ttoolbar: {\n\t\tshow: true,\n\t\tlist: [ 'ALL', 'CLEAR', 'REVERSE' ]\n\t},\n})\n")])])}],!1,null,null,null);r.options.__file="docs/mds/XM19.md";e.default=r.exports},716:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n<div>可以搜索 1, 2, 3看看效果</div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tfilterable: true,\n\tshowCount: 5,\n\tdata: [\n\t\t{name: '张三1', value: 1, selected: true},\n\t\t{name: '李四1', value: 2, selected: true},\n\t\t{name: '王五1', value: 3},\n\t\t{name: '赵六1', value: 4},\n\t\t{name: '苹果2', value: 5},\n\t\t{name: '香蕉2', value: 6},\n\t\t{name: '凤梨2', value: 7},\n\t\t{name: '葡萄2', value: 8},\n\t\t{name: '樱桃3', value: 9},\n\t\t{name: '车厘子3', value: 10},\n\t\t{name: '火龙果3', value: 11},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}}),this._v(" "),e("div",[this._v("可以搜索 1, 2, 3看看效果")])])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",filterable:!0,showCount:5,data:[{name:"张三1",value:1,selected:!0},{name:"李四1",value:2,selected:!0},{name:"王五1",value:3},{name:"赵六1",value:4},{name:"苹果2",value:5},{name:"香蕉2",value:6},{name:"凤梨2",value:7},{name:"葡萄2",value:8},{name:"樱桃3",value:9},{name:"车厘子3",value:10},{name:"火龙果3",value:11}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"xuan-xiang-xian-shi-shu-liang"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xuan-xiang-xian-shi-shu-liang"}},[this._v("¶")]),this._v(" 选项显示数量")])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"tip"},[e("p",[this._v("当数量量过大时, 又不想使用分页的形式, 就可以控制显示数量, 用搜索的方式把最适合的数据展示出来")]),e("p",[this._v("!!! 此处与分页配置互斥, 开启分页后, 此配置无效")]),e("p",[this._v("!!! 使用此配置时建议开启搜索模式, 否则无法显示全部数据")])])}],!1,null,null,null);r.options.__file="docs/mds/XM20.md";e.default=r.exports},717:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),n("demo-block",[n("div",[n("p",[t._v("选项中的"),n("code",[t._v("children")]),t._v("为数组")])]),n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\ttoolbar:{\n\t\tshow: true,\n\t},\n\tfilterable: true,\n\theight: '500px',\n\tdata: [\n\t\t{name: '销售员', children: [\n\t\t\t{name: '张三1', value: 1, selected: true},\n\t\t\t{name: '李四1', value: 2, selected: true},\n\t\t\t{name: '王五1', value: 3, disabled: true},\n\t\t]},\n\t\t{name: '奖品', children: [\n\t\t\t{name: '苹果2', value: 4, selected: true, disabled: true, children: []},\n\t\t\t{name: '香蕉2', value: 5},\n\t\t\t{name: '葡萄2', value: 6},\n\t\t]},\n\t]\n})\n<\/script>\n")])])])],2),t._m(2),n("demo-block",[n("template",{slot:"source"},[n("element-demo1")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo4\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo4 = xmSelect.render({\n\tel: '#demo4', \n\ttoolbar:{\n\t\tshow: true,\n\t},\n\theight: '300px',\n\tdata: [\n\t\t{name: '开始无分组1', value: 11, children: []},\n\t\t{name: '开始无分组2', value: 12, children: []},\n\t\t{name: '选中', optgroup: true, click: 'SELECT', children: [\n\t\t\t{name: '张三', value: 1},\n\t\t\t{name: '李四', value: 2, disabled: true},\n\t\t]},\n\t\t{name: '清空', optgroup: true, click: 'CLEAR', children: [\n\t\t\t{name: '王五', value: 3, disabled: true},\n\t\t\t{name: '苹果', value: 4, selected: true},\n\t\t]},\n\t\t{name: '自动', optgroup: true, click: 'AUTO', children: [\n\t\t\t{name: '香蕉', value: 5},\n\t\t\t{name: '葡萄', value: 6},\n\t\t]},\n\t\t{name: '中间无分组1', value: 21, children: []},\n\t\t{name: '中间无分组2', value: 22, children: []},\n\t\t{name: '自定义', optgroup: true, click: function(item){ alert('自定义的, 想干嘛干嘛') }, children: [\n\t\t\t{name: '小米', value: 7},\n\t\t\t{name: '华为', value: 8},\n\t\t]},\n\t\t{name: '结尾无分组1', value: 31, children: []},\n\t\t{name: '结尾无分组2', value: 32, children: []},\n\t]\n})\n<\/script>\n")])])])],2),t._m(3),n("demo-block",[n("template",{slot:"source"},[n("element-demo2")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo5\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo5 = xmSelect.render({\n\tel: '#demo5', \n\ttoolbar:{\n\t\tshow: true,\n\t},\n\tfilterable: true,\n\theight: '500px',\n\tpaging: true,\n\tpageSize: 2,\n\tdata: [\n\t\t{name: '销售员', children: [\n\t\t\t{name: '张三1', value: 1},\n\t\t\t{name: '李四1', value: 2},\n\t\t\t{name: '王五13', value: 3},\n\t\t]},\n\t\t{name: '奖品', children: [\n\t\t\t{name: '苹果23', value: 4},\n\t\t\t{name: '香蕉2', value: 5},\n\t\t\t{name: '葡萄2', value: 6},\n\t\t]},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",toolbar:{show:!0},filterable:!0,height:"500px",data:[{name:"销售员",children:[{name:"张三1",value:1,selected:!0},{name:"李四1",value:2,selected:!0},{name:"王五1",value:3,disabled:!0}]},{name:"奖品",children:[{name:"苹果2",value:4,selected:!0,disabled:!0,children:[]},{name:"香蕉2",value:5},{name:"葡萄2",value:6}]}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo4"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo4",toolbar:{show:!0},height:"300px",data:[{name:"开始无分组1",value:11,children:[]},{name:"开始无分组2",value:12,children:[]},{name:"选中",optgroup:!0,click:"SELECT",children:[{name:"张三",value:1},{name:"李四",value:2,disabled:!0}]},{name:"清空",optgroup:!0,click:"CLEAR",children:[{name:"王五",value:3,disabled:!0},{name:"苹果",value:4,selected:!0}]},{name:"自动",optgroup:!0,click:"AUTO",children:[{name:"香蕉",value:5},{name:"葡萄",value:6}]},{name:"中间无分组1",value:21,children:[]},{name:"中间无分组2",value:22,children:[]},{name:"自定义",optgroup:!0,click:function(t){alert("自定义的, 想干嘛干嘛")},children:[{name:"小米",value:7},{name:"华为",value:8}]},{name:"结尾无分组1",value:31,children:[]},{name:"结尾无分组2",value:32,children:[]}]})}))}}}(),"element-demo2":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo5"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo5",toolbar:{show:!0},filterable:!0,height:"500px",paging:!0,pageSize:2,data:[{name:"销售员",children:[{name:"张三1",value:1},{name:"李四1",value:2},{name:"王五13",value:3}]},{name:"奖品",children:[{name:"苹果23",value:4},{name:"香蕉2",value:5},{name:"葡萄2",value:6}]}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"fen-zu"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#fen-zu"}},[this._v("¶")]),this._v(" 分组")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"children-mo-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#children-mo-shi"}},[this._v("¶")]),this._v(" children模式")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"fen-zu-de-dan-ji-shi-jian"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#fen-zu-de-dan-ji-shi-jian"}},[this._v("¶")]),this._v(" 分组的单击事件")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"dai-you-fen-ye-de-fen-zu"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#dai-you-fen-ye-de-fen-zu"}},[this._v("¶")]),this._v(" 带有分页的分组")])}],!1,null,null,null);r.options.__file="docs/mds/XM21.md";e.default=r.exports},718:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),t._m(2),n("demo-block",[n("div",[n("p",[n("code",[t._v("autoRow")]),t._v(" = "),n("code",[t._v("true")])])]),n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\ttoolbar:{\n\t\tshow: true,\n\t},\n\tautoRow: true,\n\theight: '500px',\n\tdata: [\n\t\t{name: '张三1', value: 1, selected: true},\n\t\t{name: '李四1', value: 2, selected: true},\n\t\t{name: '王五1', value: 3, disabled: true},\n\t\t{name: '苹果2', value: 4, selected: true, disabled: true},\n\t\t{name: '香蕉2', value: 5, selected: true},\n\t\t{name: '葡萄2', value: 6},\n\t]\n})\n<\/script>\n")])])])],2),t._m(3),t._m(4),t._m(5),n("demo-block",[n("template",{slot:"source"},[n("element-demo1")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<form class=\"layui-form layui-form-pane xm-select-demo ly-label-center\" action=\"\">\n\t<div class=\"layui-form-item\" pane>\n\t\t<label class=\"layui-form-label\">居中风格</label>\n\t\t<div class=\"layui-input-block\">\n\t\t\t<div id=\"demo2\"></div>\n\t\t</div>\n\t</div>\n</form>\n\n<form class=\"layui-form layui-form-pane xm-select-demo\" action=\"\">\n\t<div class=\"layui-form-item\" pane>\n\t\t<label class=\"layui-form-label\">默认风格</label>\n\t\t<div class=\"layui-input-block\">\n\t\t\t<div id=\"demo3\"></div>\n\t\t</div>\n\t</div>\n</form>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\ttoolbar:{\n\t\tshow: true,\n\t},\n\tfilterable: true,\n\theight: '500px',\n\tautoRow: true,\n\tdata: [\n\t\t{name: '张三1', value: 1, selected: true},\n\t\t{name: '李四1', value: 2, selected: true},\n\t\t{name: '王五1', value: 3, disabled: true},\n\t\t{name: '苹果2', value: 4, selected: true, disabled: true},\n\t\t{name: '香蕉2', value: 5, selected: true},\n\t\t{name: '葡萄2', value: 6},\n\t]\n})\n\nvar demo3 = xmSelect.render({\n\tel: '#demo3', \n\ttoolbar:{\n\t\tshow: true,\n\t},\n\tfilterable: true,\n\theight: '500px',\n\tautoRow: true,\n\tdata: [\n\t\t{name: '张三1', value: 1, selected: true},\n\t\t{name: '李四1', value: 2, selected: true},\n\t\t{name: '王五1', value: 3, disabled: true},\n\t\t{name: '苹果2', value: 4, selected: true, disabled: true},\n\t\t{name: '香蕉2', value: 5, selected: true},\n\t\t{name: '葡萄2', value: 6},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",toolbar:{show:!0},autoRow:!0,height:"500px",data:[{name:"张三1",value:1,selected:!0},{name:"李四1",value:2,selected:!0},{name:"王五1",value:3,disabled:!0},{name:"苹果2",value:4,selected:!0,disabled:!0},{name:"香蕉2",value:5,selected:!0},{name:"葡萄2",value:6}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("form",{staticClass:"layui-form layui-form-pane xm-select-demo ly-label-center",attrs:{action:""}},[e("div",{staticClass:"layui-form-item",attrs:{pane:""}},[e("label",{staticClass:"layui-form-label"},[this._v("居中风格")]),this._v(" "),e("div",{staticClass:"layui-input-block"},[e("div",{attrs:{id:"demo2"}})])])]),this._v(" "),e("form",{staticClass:"layui-form layui-form-pane xm-select-demo",attrs:{action:""}},[e("div",{staticClass:"layui-form-item",attrs:{pane:""}},[e("label",{staticClass:"layui-form-label"},[this._v("默认风格")]),this._v(" "),e("div",{staticClass:"layui-input-block"},[e("div",{attrs:{id:"demo3"}})])])])])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",toolbar:{show:!0},filterable:!0,height:"500px",autoRow:!0,data:[{name:"张三1",value:1,selected:!0},{name:"李四1",value:2,selected:!0},{name:"王五1",value:3,disabled:!0},{name:"苹果2",value:4,selected:!0,disabled:!0},{name:"香蕉2",value:5,selected:!0},{name:"葡萄2",value:6}]}),xmSelect.render({el:"#demo3",toolbar:{show:!0},filterable:!0,height:"500px",autoRow:!0,data:[{name:"张三1",value:1,selected:!0},{name:"李四1",value:2,selected:!0},{name:"王五1",value:3,disabled:!0},{name:"苹果2",value:4,selected:!0,disabled:!0},{name:"香蕉2",value:5,selected:!0},{name:"葡萄2",value:6}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"zi-dong-huan-xing"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zi-dong-huan-xing"}},[this._v("¶")]),this._v(" 自动换行")])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"tip"},[e("p",[this._v("当需要选择很多选项时, 横向滚动满足不了你的需求, 可以开启自动换行")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"kai-qi-huan-xing"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#kai-qi-huan-xing"}},[this._v("¶")]),this._v(" 开启换行")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"layui-feng-ge"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#layui-feng-ge"}},[this._v("¶")]),this._v(" Layui风格")])},function(){var t=this.$createElement,e=this._self._c||t;return e("p",[this._v("前面的label居中 需要 "),e("strong",[this._v("自行")]),this._v(" 加一段css, 这里就没有内置到插件中了, 避免样式污染, 当前或许你有更好的css解决方案 ^_^")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",{staticClass:"language-css"},[this._v(".layui-form-pane .layui-form-label{\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n")])])}],!1,null,null,null);r.options.__file="docs/mds/XM22.md";e.default=r.exports},719:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tradio: true,\n\tclickClose: true,\n\tmodel: {\n\t\ticon: 'hidden',\n\t\tlabel: {\n\t\t\ttype: 'text'\n\t\t}\n\t},\n\tdata: [\n\t\t{name: '张三1', value: 1, selected: true},\n\t\t{name: '李四1', value: 2},\n\t\t{name: '王五1', value: 3, disabled: true},\n\t\t{name: '苹果2', value: 4},\n\t]\n})\n\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\tradio: true,\n\tclickClose: true,\n\ttheme: {\n\t\tcolor: '#5FB878',\n\t},\n\tmodel: {\n\t\ticon: 'hidden',\n\t\tlabel: {\n\t\t\ttype: 'text'\n\t\t}\n\t},\n\tdata: [\n\t\t{name: '张三1', value: 1, selected: true},\n\t\t{name: '李四1', value: 2},\n\t\t{name: '王五1', value: 3, disabled: true},\n\t\t{name: '苹果2', value: 4},\n\t]\n})\n<\/script>\n")])])])],2),this._m(2),e("demo-block",[e("template",{slot:"source"},[e("element-demo1")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo3\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo3 = xmSelect.render({\n\tel: '#demo3', \n\tmodel: {\n\t\ticon: 'hidden',\n\t},\n\tdata: [\n\t\t{name: '张三1', value: 1, selected: true, disabled: true},\n\t\t{name: '李四1', value: 2, selected: true},\n\t\t{name: '王五1', value: 3, disabled: true},\n\t\t{name: '苹果2', value: 4},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}}),this._v(" "),e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",radio:!0,clickClose:!0,model:{icon:"hidden",label:{type:"text"}},data:[{name:"张三1",value:1,selected:!0},{name:"李四1",value:2},{name:"王五1",value:3,disabled:!0},{name:"苹果2",value:4}]}),xmSelect.render({el:"#demo2",radio:!0,clickClose:!0,theme:{color:"#5FB878"},model:{icon:"hidden",label:{type:"text"}},data:[{name:"张三1",value:1,selected:!0},{name:"李四1",value:2},{name:"王五1",value:3,disabled:!0},{name:"苹果2",value:4}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo3"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo3",model:{icon:"hidden"},data:[{name:"张三1",value:1,selected:!0,disabled:!0},{name:"李四1",value:2,selected:!0},{name:"王五1",value:3,disabled:!0},{name:"苹果2",value:4}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"yin-cang-tu-biao"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#yin-cang-tu-biao"}},[this._v("¶")]),this._v(" 隐藏图标")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"yin-cang-dan-xuan-tu-biao"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#yin-cang-dan-xuan-tu-biao"}},[this._v("¶")]),this._v(" 隐藏单选图标")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"yin-cang-duo-xuan-tu-biao"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#yin-cang-duo-xuan-tu-biao"}},[this._v("¶")]),this._v(" 隐藏多选图标")])}],!1,null,null,null);r.options.__file="docs/mds/XM23.md";e.default=r.exports},720:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),n("demo-block",[n("div",[n("p",[n("code",[t._v("large")]),t._v(", "),n("code",[t._v("medium")]),t._v(", "),n("code",[t._v("small")]),t._v(", "),n("code",[t._v("mini")])])]),n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo1\" class=\"xm-select-size\"></div>\n<div id=\"demo2\" class=\"xm-select-size\"></div>\n<div id=\"demo3\" class=\"xm-select-size\"></div>\n<div id=\"demo4\" class=\"xm-select-size\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tsize: 'large',\n\tdata: [\n\t\t{name: 'large', value: 1, selected: true},\n\t\t{name: '李四1', value: 2},\n\t\t{name: '王五1', value: 3, disabled: true},\n\t\t{name: '苹果2', value: 4},\n\t]\n})\n\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\tsize: 'medium',\n\tdata: [\n\t\t{name: 'medium', value: 1, selected: true},\n\t\t{name: '李四1', value: 2},\n\t\t{name: '默认尺寸', value: 3, selected: true, disabled: true},\n\t\t{name: '苹果2', value: 4},\n\t]\n})\n\nvar demo3 = xmSelect.render({\n\tel: '#demo3', \n\tsize: 'small',\n\tdata: [\n\t\t{name: 'small', value: 1, selected: true},\n\t\t{name: '李四1', value: 2},\n\t\t{name: '王五1', value: 3, disabled: true},\n\t\t{name: '苹果2', value: 4},\n\t]\n})\n\nvar demo4 = xmSelect.render({\n\tel: '#demo4', \n\tsize: 'mini',\n\tdata: [\n\t\t{name: 'mini', value: 1, selected: true},\n\t\t{name: '李四1', value: 2},\n\t\t{name: '王五1', value: 3, disabled: true},\n\t\t{name: '苹果2', value: 4},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-size",attrs:{id:"demo1"}}),this._v(" "),e("div",{staticClass:"xm-select-size",attrs:{id:"demo2"}}),this._v(" "),e("div",{staticClass:"xm-select-size",attrs:{id:"demo3"}}),this._v(" "),e("div",{staticClass:"xm-select-size",attrs:{id:"demo4"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",size:"large",data:[{name:"large",value:1,selected:!0},{name:"李四1",value:2},{name:"王五1",value:3,disabled:!0},{name:"苹果2",value:4}]}),xmSelect.render({el:"#demo2",size:"medium",data:[{name:"medium",value:1,selected:!0},{name:"李四1",value:2},{name:"默认尺寸",value:3,selected:!0,disabled:!0},{name:"苹果2",value:4}]}),xmSelect.render({el:"#demo3",size:"small",data:[{name:"small",value:1,selected:!0},{name:"李四1",value:2},{name:"王五1",value:3,disabled:!0},{name:"苹果2",value:4}]}),xmSelect.render({el:"#demo4",size:"mini",data:[{name:"mini",value:1,selected:!0},{name:"李四1",value:2},{name:"王五1",value:3,disabled:!0},{name:"苹果2",value:4}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"chi-cun"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#chi-cun"}},[this._v("¶")]),this._v(" 尺寸")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"si-zhong-chi-cun-bian-huan"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#si-zhong-chi-cun-bian-huan"}},[this._v("¶")]),this._v(" 四种尺寸变换")])}],!1,null,null,null);r.options.__file="docs/mds/XM24.md";e.default=r.exports},721:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),t._m(2),t._m(3),n("demo-block",[n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n<button class=\"btn\" id=\"demo1-warning\">警告</button>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3, disabled: true},\n\t]\n})\n\ndocument.getElementById('demo1-warning').onclick = function(){\n\tdemo1.warning();\n}\n<\/script>\n")])])])],2),t._m(4),n("demo-block",[n("template",{slot:"source"},[n("element-demo1")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n<button class=\"btn\" id=\"demo2-warning\">警告</button>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3, disabled: true},\n\t]\n})\n\ndocument.getElementById('demo2-warning').onclick = function(){\n\tdemo2.warning('#6739b6');\n}\n<\/script>\n")])])])],2),t._m(5),n("demo-block",[n("template",{slot:"source"},[n("element-demo2")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo3\" class=\"xm-select-demo\"></div>\n<button class=\"btn\" id=\"demo3-warning\">警告</button>\n\n<script>\nvar demo3 = xmSelect.render({\n\tel: '#demo3', \n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3, disabled: true},\n\t]\n})\n\ndocument.getElementById('demo3-warning').onclick = function(){\n\tdemo3.warning('#6739b6', true);\n}\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}}),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"demo1-warning"}},[this._v("警告")])])}],mounted:function(){this.$nextTick((function(){var t=xmSelect.render({el:"#demo1",data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3,disabled:!0}]});document.getElementById("demo1-warning").onclick=function(){t.warning()}}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}}),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"demo2-warning"}},[this._v("警告")])])}],mounted:function(){this.$nextTick((function(){var t=xmSelect.render({el:"#demo2",data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3,disabled:!0}]});document.getElementById("demo2-warning").onclick=function(){t.warning("#6739b6")}}))}}}(),"element-demo2":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo3"}}),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"demo3-warning"}},[this._v("警告")])])}],mounted:function(){this.$nextTick((function(){var t=xmSelect.render({el:"#demo3",data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3,disabled:!0}]});document.getElementById("demo3-warning").onclick=function(){t.warning("#6739b6",!0)}}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"jing-gao"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#jing-gao"}},[this._v("¶")]),this._v(" 警告")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"jing-gao-ti-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#jing-gao-ti-shi"}},[this._v("¶")]),this._v(" 警告提示")])},function(){var t=this.$createElement,e=this._self._c||t;return e("p",[this._v("放下她, 让我来!!!  默认颜色"),e("code",[this._v("#e54d42")]),this._v(", 多选上限的样色")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("/*\n *  COLOR: 自定义颜色, 默认是 options.theme.maxColor的颜色\n *  SUSTAIN: 是否持续显示, 默认为false\n */\nxmSelectObj.warning(COLOR, SUSTAIN);\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"zi-ding-yi-shan-shuo-yan-se"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zi-ding-yi-shan-shuo-yan-se"}},[this._v("¶")]),this._v(" 自定义闪烁颜色")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"chi-xu-xian-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#chi-xu-xian-shi"}},[this._v("¶")]),this._v(" 持续显示")])}],!1,null,null,null);r.options.__file="docs/mds/XM25.md";e.default=r.exports},722:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),t._m(2),n("demo-block",[n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tdisabled: true,\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3, disabled: true},\n\t]\n})\n<\/script>\n")])])])],2),t._m(3),n("demo-block",[n("template",{slot:"source"},[n("element-demo1")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n<button class=\"btn\" id=\"demo21\">禁用</button>\n<button class=\"btn\" id=\"demo22\">启用</button>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3, disabled: true},\n\t]\n})\n\ndocument.getElementById('demo21').onclick = function(){\n\tdemo2.update({ disabled: true });\n}\ndocument.getElementById('demo22').onclick = function(){\n\tdemo2.update({ disabled: false });\n}\n<\/script>\n")])])])],2),t._m(4),n("demo-block",[n("template",{slot:"source"},[n("element-demo2")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo3\" class=\"xm-select-demo\"></div>\n<button class=\"btn\" id=\"demo3-disabled\">启用</button>\n\n<script>\nvar demo3 = xmSelect.render({\n\tel: '#demo3', \n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t],\n\ton({ arr, change, isAdd }){\n\t\tvar hasZS = change.find(item => item.name === '张三');\n\t\tif(isAdd && hasZS){\n\t\t\tdemo3.update({ disabled: true });\n\t\t}\n\t}\n})\n\n//没有做不到, 只有想不到, 把多选玩出花来吧\ndocument.getElementById('demo3-disabled').onclick = function(){\n\tdemo3.update({ disabled: false });\n}\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",disabled:!0,data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3,disabled:!0}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}}),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"demo21"}},[this._v("禁用")]),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"demo22"}},[this._v("启用")])])}],mounted:function(){this.$nextTick((function(){var t=xmSelect.render({el:"#demo2",data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3,disabled:!0}]});document.getElementById("demo21").onclick=function(){t.update({disabled:!0})},document.getElementById("demo22").onclick=function(){t.update({disabled:!1})}}))}}}(),"element-demo2":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo3"}}),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"demo3-disabled"}},[this._v("启用")])])}],mounted:function(){this.$nextTick((function(){var t=xmSelect.render({el:"#demo3",data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}],on:function(e){e.arr;var n=e.change,a=e.isAdd,i=n.find((function(t){return"张三"===t.name}));a&&i&&t.update({disabled:!0})}});document.getElementById("demo3-disabled").onclick=function(){t.update({disabled:!1})}}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"jin-yong"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#jin-yong"}},[this._v("¶")]),this._v(" 禁用")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"xuan-ran-jin-yong"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xuan-ran-jin-yong"}},[this._v("¶")]),this._v(" 渲染禁用")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("xmSelect.render({\n\t//...\n\tdisabled: true\n})\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"dong-tai-qi-yong-jin-yong"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#dong-tai-qi-yong-jin-yong"}},[this._v("¶")]),this._v(" 动态启用禁用")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"xuan-wan-zhang-san-hou-jin-yong"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xuan-wan-zhang-san-hou-jin-yong"}},[this._v("¶")]),this._v(" 选完"),e("code",[this._v("张三")]),this._v("后禁用")])}],!1,null,null,null);r.options.__file="docs/mds/XM26.md";e.default=r.exports},723:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),this._m(2),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tfilterable: true,\n\tcreate: function(val, arr){\n\t\tif(arr.length === 0){\n\t\t\treturn {\n\t\t\t\tname: '创建-' + val,\n\t\t\t\tvalue: val\n\t\t\t}\n\t\t}\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3, disabled: true},\n\t]\n})\n<\/script>\n")])])])],2),this._m(3),e("demo-block",[e("template",{slot:"source"},[e("element-demo1")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo2', \n\tradio: true,\n\tclickClose: true,\n\tfilterable: true,\n\tcreate: function(val, arr){\n\t\tif(arr.length === 0){\n\t\t\treturn {\n\t\t\t\tname: '创建-' + val,\n\t\t\t\tvalue: val\n\t\t\t}\n\t\t}\n\t},\n\tmodel: {\n\t\ticon: 'hidden',\n\t\tlabel: {\n\t\t\ttype: 'text',\n\t\t}\n\t},\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",filterable:!0,create:function(t,e){if(0===e.length)return{name:"创建-"+t,value:t}},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3,disabled:!0}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",radio:!0,clickClose:!0,filterable:!0,create:function(t,e){if(0===e.length)return{name:"创建-"+t,value:t}},model:{icon:"hidden",label:{type:"text"}},data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"chuang-jian-tiao-mu"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#chuang-jian-tiao-mu"}},[this._v("¶")]),this._v(" 创建条目")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"sou-suo-bu-cun-zai-ze-chuang-jian-tiao-mu"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#sou-suo-bu-cun-zai-ze-chuang-jian-tiao-mu"}},[this._v("¶")]),this._v(" 搜索不存在则创建条目")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("//想创建就必须要开启本地搜索\nxmSelect.render({\n\t//...\n\tfilterable: true,\n\tcreate: function(val, arr){\n\t\t//返回一个创建成功的对象, val是搜索的数据, arr是搜索后的当前页面数据\n\t\treturn {\n\t\t\tname: '创建-' + val,\n\t\t\tvalue: val\n\t\t}\n\t}\n})\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"dan-xuan-chuang-jian"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#dan-xuan-chuang-jian"}},[this._v("¶")]),this._v(" 单选创建")])}],!1,null,null,null);r.options.__file="docs/mds/XM27.md";e.default=r.exports},724:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),this._m(2),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v('<div id="demo1" class="xm-select-demo"></div>\n<br/><br/>\n<button class="btn" id="demo1-test1">赋值张三</button>\n<button class="btn" id="demo1-test2">赋值张三(value方式)</button>\n<button class="btn" id="demo1-test3">追加赋值李四</button>\n<br/><br/>\n<button class="btn" id="demo1-test4">清除李四</button>\n<button class="btn" id="demo1-test5">清空</button>\n<pre id="demo1-result"></pre>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: \'#demo1\', \n\tdata: [\n\t\t{name: \'张三\', value: 1},\n\t\t{name: \'李四\', value: 2},\n\t\t{name: \'王五\', value: 3},\n\t]\n})\n\ndocument.getElementById(\'demo1-test1\').onclick = function(){\n\tdemo1.setValue([\n\t\t{name: \'张三\', value: 1},\n\t])\n};\n\ndocument.getElementById(\'demo1-test2\').onclick = function(){\n\tdemo1.setValue([ 1 ])\n};\n\ndocument.getElementById(\'demo1-test3\').onclick = function(){\n\tdemo1.append([ 2 ]);\n};\n\ndocument.getElementById(\'demo1-test4\').onclick = function(){\n\tdemo1.delete([ 2 ])\n};\n\ndocument.getElementById(\'demo1-test5\').onclick = function(){\n\tdemo1.setValue([ ])\n};\n\n<\/script>\n')])])])],2),this._m(3),this._m(4),e("demo-block",[e("template",{slot:"source"},[e("element-demo1")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v('<div id="demo2" class="xm-select-demo"></div>\n<button class="btn" id="demo2-getValue">获取选中值</button>\n\n<br/><br/>\n\n<button class="btn" id="name">获取name数组</button>\n<button class="btn" id="nameStr">获取name字符串</button>\n<button class="btn" id="value">获取value数组</button>\n<button class="btn" id="valueStr">获取value字符串</button>\n\n<pre id="demo2-value"></pre>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: \'#demo2\', \n\tdata: [\n\t\t{name: \'张三\', value: 1},\n\t\t{name: \'李四\', value: 2},\n\t\t{name: \'王五\', value: 3},\n\t]\n})\n\ndocument.getElementById(\'demo2-getValue\').onclick = function(){\n\t//获取当前多选选中的值\n\tvar selectArr = demo2.getValue();\n\tdocument.getElementById(\'demo2-value\').innerHTML = `\\ndemo2.getValue()\\n\\n` + JSON.stringify(selectArr, null, 2);\n}\n\nvar types = [\'name\', \'nameStr\', \'value\', \'valueStr\'];\ntypes.forEach(function(type){\n\tdocument.getElementById(type).onclick = function(){\n\t\t//获取当前多选选中的值\n\t\tvar selectArr = demo2.getValue(type);\n\t\tdocument.getElementById(\'demo2-value\').innerHTML = `\\ndemo2.getValue(\'${type}\')\\n\\n` + JSON.stringify(selectArr, null, 2);\n\t}\n});\n\n\n<\/script>\n\n')])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}}),t._v(" "),n("br"),n("br"),t._v(" "),n("button",{staticClass:"btn",attrs:{id:"demo1-test1"}},[t._v("赋值张三")]),t._v(" "),n("button",{staticClass:"btn",attrs:{id:"demo1-test2"}},[t._v("赋值张三(value方式)")]),t._v(" "),n("button",{staticClass:"btn",attrs:{id:"demo1-test3"}},[t._v("追加赋值李四")]),t._v(" "),n("br"),n("br"),t._v(" "),n("button",{staticClass:"btn",attrs:{id:"demo1-test4"}},[t._v("清除李四")]),t._v(" "),n("button",{staticClass:"btn",attrs:{id:"demo1-test5"}},[t._v("清空")]),t._v(" "),n("pre",{attrs:{id:"demo1-result"}})])}],mounted:function(){this.$nextTick((function(){var t=xmSelect.render({el:"#demo1",data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}]});document.getElementById("demo1-test1").onclick=function(){t.setValue([{name:"张三",value:1}])},document.getElementById("demo1-test2").onclick=function(){t.setValue([1])},document.getElementById("demo1-test3").onclick=function(){t.append([2])},document.getElementById("demo1-test4").onclick=function(){t.delete([2])},document.getElementById("demo1-test5").onclick=function(){t.setValue([])}}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}}),t._v(" "),n("button",{staticClass:"btn",attrs:{id:"demo2-getValue"}},[t._v("获取选中值")]),t._v(" "),n("br"),n("br"),t._v(" "),n("button",{staticClass:"btn",attrs:{id:"name"}},[t._v("获取name数组")]),t._v(" "),n("button",{staticClass:"btn",attrs:{id:"nameStr"}},[t._v("获取name字符串")]),t._v(" "),n("button",{staticClass:"btn",attrs:{id:"value"}},[t._v("获取value数组")]),t._v(" "),n("button",{staticClass:"btn",attrs:{id:"valueStr"}},[t._v("获取value字符串")]),t._v(" "),n("pre",{attrs:{id:"demo2-value"}})])}],mounted:function(){this.$nextTick((function(){var t=xmSelect.render({el:"#demo2",data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}]});document.getElementById("demo2-getValue").onclick=function(){var e=t.getValue();document.getElementById("demo2-value").innerHTML="\ndemo2.getValue()\n\n"+JSON.stringify(e,null,2)};["name","nameStr","value","valueStr"].forEach((function(e){document.getElementById(e).onclick=function(){var n=t.getValue(e);document.getElementById("demo2-value").innerHTML="\ndemo2.getValue('".concat(e,"')\n\n")+JSON.stringify(n,null,2)}}))}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"fu-zhi-yu-qu-zhi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#fu-zhi-yu-qu-zhi"}},[this._v("¶")]),this._v(" 赋值与取值")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"gei-duo-xuan-fu-zhi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#gei-duo-xuan-fu-zhi"}},[this._v("¶")]),this._v(" 给多选赋值")])},function(){var t=this.$createElement,e=this._self._c||t;return e("p",[this._v("函数"),e("code",[this._v("setValue(array)")]),this._v(", 动态赋值多选选中的数据, array可以是value数组, 也可以是完整的object数组")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"qu-zhi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#qu-zhi"}},[this._v("¶")]),this._v(" 取值")])},function(){var t=this.$createElement,e=this._self._c||t;return e("p",[this._v("函数"),e("code",[this._v("getValue(type)")]),this._v(", type类型 name, nameStr, value, valueStr")])}],!1,null,null,null);r.options.__file="docs/mds/ZM01.md";e.default=r.exports},725:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<form>\n\t<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\t<button class=\"btn\" type=\"submit\">提交</button>\n</form>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n\n<\/script>\n")])])])],2),this._m(2),this._m(3),e("demo-block",[e("template",{slot:"source"},[e("element-demo1")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<form class=\"layui-form\">\n\t<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\t<button class=\"btn\" type=\"submit\" lay-submit>提交</button>\n\t<button class=\"btn\" type=\"reset\">重置</button>\n</form>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\tname: 'lalalalalala',\n\tlayVerify: 'required',\n\tlayVerType: 'msg',\n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("form",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}}),this._v(" "),e("button",{staticClass:"btn",attrs:{type:"submit"}},[this._v("提交")])])])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("form",{staticClass:"layui-form"},[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}}),this._v(" "),e("button",{staticClass:"btn",attrs:{type:"submit","lay-submit":""}},[this._v("提交")]),this._v(" "),e("button",{staticClass:"btn",attrs:{type:"reset"}},[this._v("重置")])])])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",name:"lalalalalala",layVerify:"required",layVerType:"msg",data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}]})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"biao-dan-ti-jiao"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#biao-dan-ti-jiao"}},[this._v("¶")]),this._v(" 表单提交")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"mo-ren-biao-dan-ti-jiao"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#mo-ren-biao-dan-ti-jiao"}},[this._v("¶")]),this._v(" 默认表单提交")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"xiu-gainame-biao-dan-yan-zheng"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xiu-gainame-biao-dan-yan-zheng"}},[this._v("¶")]),this._v(" 修改name, 表单验证")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("{\n\tname: 'lalalalalala',\n\tlayVerify: 'required',\n\tlayVerType: 'tips',\n}\n")])])}],!1,null,null,null);r.options.__file="docs/mds/ZM02.md";e.default=r.exports},726:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v('\n<table id="demo" lay-filter="test"></table>\n\n<script>\nlayui.use(\'table\', function() {\n\tvar table = layui.table;\n\n\t//第一个实例\n\ttable.render({\n\t\telem: \'#demo\',\n\t\tpage: true, //开启分页\n\t\theight: 500,\n\t\tcols: [\n\t\t\t[ //表头\n\t\t\t\t{ field: \'id\', title: \'ID\', width: 80, sort: true }, \n\t\t\t\t{ field: \'username\', title: \'用户名\', width: 80 }, \n\t\t\t\t{ field: \'sex\', title: \'性别\', width: 80, sort: true }, \n\t\t\t\t{ field: \'city\', title: \'城市\', width: 80 }, \n\t\t\t\t{ field: \'sign\', title: \'爱好\', width: 200, templet: function(d){\n\t\t\t\t\treturn \'<div id="XM-\' + d.id + \'" ></div>\'\n\t\t\t\t} }, \n\t\t\t\t{ field: \'experience\', title: \'积分\', width: 80, sort: true }, \n\t\t\t\t{ field: \'score\', title: \'评分\', width: 80, sort: true }, \n\t\t\t\t{ field: \'classify\', title: \'职业\', width: 80 }, \n\t\t\t\t{ field: \'wealth\', title: \'财富\', width: 135, sort: true }\n\t\t\t]\n\t\t],\n\t\tdata: [\n\t\t\t{"id":10000,"username":"user-0","sex":"女","city":"城市-0","sign":"签名-0","experience":255,"logins":24,"wealth":82830700,"classify":"作家","score":57},\n\t\t\t{"id":10001,"username":"user-1","sex":"男","city":"城市-1","sign":"签名-1","experience":884,"logins":58,"wealth":64928690,"classify":"词人","score":27},\n\t\t\t{"id":10002,"username":"user-2","sex":"女","city":"城市-2","sign":"签名-2","experience":650,"logins":77,"wealth":6298078,"classify":"酱油","score":31},\n\t\t\t{"id":10003,"username":"user-3","sex":"女","city":"城市-3","sign":"签名-3","experience":362,"logins":157,"wealth":37117017,"classify":"诗人","score":68},\n\t\t\t{"id":10004,"username":"user-4","sex":"男","city":"城市-4","sign":"签名-4","experience":807,"logins":51,"wealth":76263262,"classify":"作家","score":6},\n\t\t\t{"id":10005,"username":"user-5","sex":"女","city":"城市-5","sign":"签名-5","experience":173,"logins":68,"wealth":60344147,"classify":"作家","score":87},\n\t\t\t{"id":10006,"username":"user-6","sex":"女","city":"城市-6","sign":"签名-6","experience":982,"logins":37,"wealth":57768166,"classify":"作家","score":34},\n\t\t\t{"id":10007,"username":"user-7","sex":"男","city":"城市-7","sign":"签名-7","experience":727,"logins":150,"wealth":82030578,"classify":"作家","score":28},\n\t\t\t{"id":10008,"username":"user-8","sex":"男","city":"城市-8","sign":"签名-8","experience":951,"logins":133,"wealth":16503371,"classify":"词人","score":14},\n\t\t\t{"id":10009,"username":"user-9","sex":"女","city":"城市-9","sign":"签名-9","experience":484,"logins":25,"wealth":86801934,"classify":"词人","score":75}\n\t\t],\n\t\tdone: function(res){\n\t\t\t//修改一些css样式, 这里虽然能够使用, 但是还是不太友好, 努力中...\n\t\t\tvar cells = document.querySelectorAll(\'div[lay-id="demo"] .layui-table-cell\');\n\t\t\tfor(var i = 0 ; i < cells.length ; i++ ){\n\t\t\t\t//cells[i].style.overflow = \'unset\';\n\t\t\t\tcells[i].style.height = \'auto\';\n\t\t\t}\n\t\t\t//渲染多选\n\t\t\tres.data.forEach(item =>  {\n\t\t\t\tvar xm = xmSelect.render({\n\t\t\t\t\tel: \'#XM-\' + item.id, \n\t\t\t\t\tautoRow: true,\n\t\t\t\t\tmodel: { type: \'fixed\' },\n\t\t\t\t\tdata: [\n\t\t\t\t\t\t{name: \'张三\', value: 1},\n\t\t\t\t\t\t{name: \'李四\', value: 2},\n\t\t\t\t\t\t{name: \'王五\', value: 3},\n\t\t\t\t\t]\n\t\t\t\t})\n\t\t\t\t\n\t\t\t\titem.__xm = xm;\n\t\t\t})\n\t\t}\n\t});\n\n});\n\n//表格滚动时 重新计算位置\ndocument.querySelector(\'.layui-table-body\').addEventListener(\'scroll\', () => {\n\txmSelect.get().forEach(function(item){\n\t\titem.calcPosition();\n\t})\n})\n\n\n<\/script>\n')])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("table",{attrs:{id:"demo","lay-filter":"test"}})])}],mounted:function(){this.$nextTick((function(){layui.use("table",(function(){layui.table.render({elem:"#demo",page:!0,height:500,cols:[[{field:"id",title:"ID",width:80,sort:!0},{field:"username",title:"用户名",width:80},{field:"sex",title:"性别",width:80,sort:!0},{field:"city",title:"城市",width:80},{field:"sign",title:"爱好",width:200,templet:function(t){return'<div id="XM-'+t.id+'" ></div>'}},{field:"experience",title:"积分",width:80,sort:!0},{field:"score",title:"评分",width:80,sort:!0},{field:"classify",title:"职业",width:80},{field:"wealth",title:"财富",width:135,sort:!0}]],data:[{id:1e4,username:"user-0",sex:"女",city:"城市-0",sign:"签名-0",experience:255,logins:24,wealth:82830700,classify:"作家",score:57},{id:10001,username:"user-1",sex:"男",city:"城市-1",sign:"签名-1",experience:884,logins:58,wealth:64928690,classify:"词人",score:27},{id:10002,username:"user-2",sex:"女",city:"城市-2",sign:"签名-2",experience:650,logins:77,wealth:6298078,classify:"酱油",score:31},{id:10003,username:"user-3",sex:"女",city:"城市-3",sign:"签名-3",experience:362,logins:157,wealth:37117017,classify:"诗人",score:68},{id:10004,username:"user-4",sex:"男",city:"城市-4",sign:"签名-4",experience:807,logins:51,wealth:76263262,classify:"作家",score:6},{id:10005,username:"user-5",sex:"女",city:"城市-5",sign:"签名-5",experience:173,logins:68,wealth:60344147,classify:"作家",score:87},{id:10006,username:"user-6",sex:"女",city:"城市-6",sign:"签名-6",experience:982,logins:37,wealth:57768166,classify:"作家",score:34},{id:10007,username:"user-7",sex:"男",city:"城市-7",sign:"签名-7",experience:727,logins:150,wealth:82030578,classify:"作家",score:28},{id:10008,username:"user-8",sex:"男",city:"城市-8",sign:"签名-8",experience:951,logins:133,wealth:16503371,classify:"词人",score:14},{id:10009,username:"user-9",sex:"女",city:"城市-9",sign:"签名-9",experience:484,logins:25,wealth:86801934,classify:"词人",score:75}],done:function(t){for(var e=document.querySelectorAll('div[lay-id="demo"] .layui-table-cell'),n=0;n<e.length;n++)e[n].style.height="auto";t.data.forEach((function(t){var e=xmSelect.render({el:"#XM-"+t.id,autoRow:!0,model:{type:"fixed"},data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}]});t.__xm=e}))}})})),document.querySelector(".layui-table-body").addEventListener("scroll",(function(){xmSelect.get().forEach((function(t){t.calcPosition()}))}))}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"biao-ge-zhong-duo-xuan"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#biao-ge-zhong-duo-xuan"}},[this._v("¶")]),this._v(" 表格中多选")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"layui-biao-ge-zhong-duo-xuan"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#layui-biao-ge-zhong-duo-xuan"}},[this._v("¶")]),this._v(" Layui表格中多选")])}],!1,null,null,null);r.options.__file="docs/mds/ZM03.md";e.default=r.exports},727:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo1\"></div>\n\n<script>\n\tvar demo1 = xmSelect.render({\n\t\tel: '#demo1', \n\t\tautoRow: true,\n\t\ttoolbar: { show: true },\n\t\tfilterable: true,\n\t\tremoteSearch: true,\n\t\tremoteMethod: function(val, cb, show){\n\t\t\t//这里如果val为空, 则不触发搜索\n\t\t\tif(!val){\n\t\t\t\treturn cb([]);\n\t\t\t}\n\t\t\t//这里引入了一个第三方插件axios, 相当于$.ajax\n\t\t\taxios({\n\t\t\t\tmethod: 'get',\n\t\t\t\turl: 'https://www.fastmock.site/mock/98228b1f16b7e5112d6c0c87921eabc1/xmSelect/search',\n\t\t\t\tparams: {\n\t\t\t\t\tkeyword: val,\n\t\t\t\t}\n\t\t\t}).then(response => {\n\t\t\t\tvar res = response.data;\n\t\t\t\tcb(res.data)\n\t\t\t}).catch(err => {\n\t\t\t\tcb([]);\n\t\t\t});\n\t\t},\n\t})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",autoRow:!0,toolbar:{show:!0},filterable:!0,remoteSearch:!0,remoteMethod:function(t,e,n){if(!t)return e([]);axios({method:"get",url:"https://www.fastmock.site/mock/98228b1f16b7e5112d6c0c87921eabc1/xmSelect/search",params:{keyword:t}}).then((function(t){var n=t.data;e(n.data)})).catch((function(t){e([])}))}})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"yuan-cheng-sou-suo"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#yuan-cheng-sou-suo"}},[this._v("¶")]),this._v(" 远程搜索")])}],!1,null,null,null);r.options.__file="docs/mds/ZM04.md";e.default=r.exports},728:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tdata: []\n})\n\ndemo1.update({\n\tdata: [\n\t\t{name: '张三', value: 1, selected: true},\n\t\t{name: '李四', value: 2, selected: true},\n\t\t{name: '王五', value: 3, disabled: true},\n\t]\n})\n\n<\/script>\n")])])])],2),this._m(2),e("demo-block",[e("template",{slot:"source"},[e("element-demo1")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\ttoolbar: {show: true},\n\tdata: []\n})\n\naxios({\n\tmethod: 'get',\n\turl: 'https://www.fastmock.site/mock/98228b1f16b7e5112d6c0c87921eabc1/xmSelect/search',\n}).then(response => {\n\tvar res = response.data;\n\t\n\tdemo2.update({\n\t\tdata: res.data,\n\t\tautoRow: true,\n\t})\n});\n\n<\/script>\n")])])])],2),this._m(3),e("demo-block",[e("template",{slot:"source"},[e("element-demo2")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<table id=\"form-create\" class=\"layui-table\">\n\t<thead>\n\t\t<tr>\n\t\t\t<th style=\"width: 50px;\">序号</th>\n\t\t\t<th>性别</th>\n\t\t\t<th>爱好</th>\n\t\t\t<th style=\"width: 150px;\">操作</th>\n\t\t</tr>\n\t</thead>\n\t<tbody>\n\t\t<tr>\n\t\t\t<td colspan=\"3\">\n\t\t\t\t<span>这里呢, 演示一下动态添加多选的例子</span>\n\t\t\t</td>\n\t\t\t<td colspan=\"1\">\n\t\t\t\t<div class=\"layui-btn-group\">\n\t\t\t\t\t<button type=\"button\" class=\"layui-btn add\">增加</button>\n\t\t\t\t</div>\n\t\t\t</td>\n\t\t</tr>\n\t</tbody>\n</table>\n\n<script>\nvar $ = layui.jquery;\nvar index = 1;\n\t\t\t\t\n$('.add').on('click', function(){\n\tvar element = $([\n\t\t'<tr>',\n\t\t\t'<td>'+ index +'</td>',\n\t\t\t'<td class=\"gender\"></td>',\n\t\t\t'<td class=\"hobby\"></td>',\n\t\t\t'<td class=\"handler\">',\n\t\t\t\t'<button type=\"button\" class=\"layui-btn layui-btn-normal get\">取值</button>',\n\t\t\t\t'<button type=\"button\" class=\"layui-btn layui-btn-danger del\">删除</button>',\n\t\t\t'</td>',\n\t\t'</tr>',\n\t].join(''))\n\t\n\tvar hobby = element.find('.hobby')[0];\n\tvar hobbySelect = xmSelect.render({\n\t\tel: hobby,\n\t\tdata: function(){\n\t\t\treturn [\n\t\t\t\t{name: '篮球' + index, value: 1},\n\t\t\t\t{name: '足球' + index, value: 2},\n\t\t\t\t{name: '乒乓球' + index, value: 3},\n\t\t\t]\n\t\t}\n\t})\n\t\n\tvar gender = element.find('.gender')[0];\n\txmSelect.render({\n\t\tel: gender,\n\t\tradio: true,\n\t\tclickClose: true,\n\t\tmodel: { label: { type: 'text' } },\n\t\tdata: function(){\n\t\t\treturn [\n\t\t\t\t{name: '男', value: 1},\n\t\t\t\t{name: '女', value: 2},\n\t\t\t\t{name: '保密', value: 3},\n\t\t\t]\n\t\t},\n\t\ton: function(data){\n\t\t\tvar changeItem = data.change[0];\n\t\t\tif(data.isAdd && changeItem.value == 3){\n\t\t\t\tthis.update({ disabled: true })\n\t\t\t}else{\n\t\t\t\tthis.update({ disabled: false })\n\t\t\t}\n\t\t}.bind(hobbySelect),\n\t})\n\t\n\telement.find('.get').on('click', function(){\n\t\talert('valueStr: ' + this.getValue('valueStr'));\n\t}.bind(hobbySelect))\n\t\n\telement.find('.del').on('click', function(){\n\t\t$(this).parents('tr').remove();\n\t})\n\t\n\tindex++;\n\t\n\t$('#form-create tbody').append(element)\n});\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",data:[]}).update({data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2,selected:!0},{name:"王五",value:3,disabled:!0}]})}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){var t=xmSelect.render({el:"#demo2",toolbar:{show:!0},data:[]});axios({method:"get",url:"https://www.fastmock.site/mock/98228b1f16b7e5112d6c0c87921eabc1/xmSelect/search"}).then((function(e){var n=e.data;t.update({data:n.data,autoRow:!0})}))}))}}}(),"element-demo2":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("table",{staticClass:"layui-table",attrs:{id:"form-create"}},[n("thead",[n("tr",[n("th",{staticStyle:{width:"50px"}},[t._v("序号")]),t._v(" "),n("th",[t._v("性别")]),t._v(" "),n("th",[t._v("爱好")]),t._v(" "),n("th",{staticStyle:{width:"150px"}},[t._v("操作")])])]),t._v(" "),n("tbody",[n("tr",[n("td",{attrs:{colspan:"3"}},[n("span",[t._v("这里呢, 演示一下动态添加多选的例子")])]),t._v(" "),n("td",{attrs:{colspan:"1"}},[n("div",{staticClass:"layui-btn-group"},[n("button",{staticClass:"layui-btn add",attrs:{type:"button"}},[t._v("增加")])])])])])])])}],mounted:function(){this.$nextTick((function(){var t=layui.jquery,e=1;t(".add").on("click",(function(){var n=t(["<tr>","<td>"+e+"</td>",'<td class="gender"></td>','<td class="hobby"></td>','<td class="handler">','<button type="button" class="layui-btn layui-btn-normal get">取值</button>','<button type="button" class="layui-btn layui-btn-danger del">删除</button>',"</td>","</tr>"].join("")),a=n.find(".hobby")[0],i=xmSelect.render({el:a,data:function(){return[{name:"篮球"+e,value:1},{name:"足球"+e,value:2},{name:"乒乓球"+e,value:3}]}}),s=n.find(".gender")[0];xmSelect.render({el:s,radio:!0,clickClose:!0,model:{label:{type:"text"}},data:function(){return[{name:"男",value:1},{name:"女",value:2},{name:"保密",value:3}]},on:function(t){var e=t.change[0];t.isAdd&&3==e.value?this.update({disabled:!0}):this.update({disabled:!1})}.bind(i)}),n.find(".get").on("click",function(){alert("valueStr: "+this.getValue("valueStr"))}.bind(i)),n.find(".del").on("click",(function(){t(this).parents("tr").remove()})),e++,t("#form-create tbody").append(n)}))}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"dong-tai-shu-ju"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#dong-tai-shu-ju"}},[this._v("¶")]),this._v(" 动态数据")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"ben-di-shu-ju-dong-tai-fu-zhi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#ben-di-shu-ju-dong-tai-fu-zhi"}},[this._v("¶")]),this._v(" 本地数据动态赋值")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"yuan-cheng-shu-ju-dong-tai-fu-zhi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#yuan-cheng-shu-ju-dong-tai-fu-zhi"}},[this._v("¶")]),this._v(" 远程数据动态赋值")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"dong-tai-chuang-jianxm-select-duo-xuan"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#dong-tai-chuang-jianxm-select-duo-xuan"}},[this._v("¶")]),this._v(" 动态创建xm-select多选")])}],!1,null,null,null);r.options.__file="docs/mds/ZM05.md";e.default=r.exports},729:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<button class=\"btn\" id=\"demo1-btn\">弹出多选</button>\n\n<script>\ndocument.getElementById('demo1-btn').onclick = function(){\n\tlayer.open({\n\t\ttype: 1,\n\t\ttitle: '多选',\n\t\tcontent: '<div id=\"demo1\" class=\"xm-select-demo-alert\"></div>',\n\t\tsuccess: function(layero, index){\n\t\t\t//这里因为内容过少, 会被遮挡, 所以简单修改了下样式\n\t\t\tdocument.getElementById('layui-layer' + index).getElementsByClassName('layui-layer-content')[0].style.overflow = 'unset';\n\t\t\t//渲染多选\n\t\t\tvar demo1 = xmSelect.render({\n\t\t\t\tel: '#demo1', \n\t\t\t\tdata: [\n\t\t\t\t\t{name: '张三', value: 1, selected: true},\n\t\t\t\t\t{name: '李四', value: 2, disabled: true},\n\t\t\t\t\t{name: '王五', value: 3},\n\t\t\t\t]\n\t\t\t})\n\t\t}\n\t});\n}\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("button",{staticClass:"btn",attrs:{id:"demo1-btn"}},[this._v("弹出多选")])])}],mounted:function(){this.$nextTick((function(){document.getElementById("demo1-btn").onclick=function(){layer.open({type:1,title:"多选",content:'<div id="demo1" class="xm-select-demo-alert"></div>',success:function(t,e){document.getElementById("layui-layer"+e).getElementsByClassName("layui-layer-content")[0].style.overflow="unset";xmSelect.render({el:"#demo1",data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2,disabled:!0},{name:"王五",value:3}]})}})}}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"dan-kuang-zhong-de-duo-xuan"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#dan-kuang-zhong-de-duo-xuan"}},[this._v("¶")]),this._v(" 弹框中的多选")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"layer-dan-chu-kuang"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#layer-dan-chu-kuang"}},[this._v("¶")]),this._v(" layer弹出框")])}],!1,null,null,null);r.options.__file="docs/mds/ZM06.md";e.default=r.exports},730:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),this._m(2),this._m(3),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n<div id=\"demo2\" class=\"xm-select-demo\"></div>\n<div id=\"xm3\" class=\"xm-select-demo\"></div>\n\n<div><button class=\"btn\" id=\"btn1\">get(所有)</button></div>\n<div><button class=\"btn\" id=\"btn2\">get(字符串)</button></div>\n<div><button class=\"btn\" id=\"btn3\">get(正则)</button></div>\n<div><button class=\"btn\" id=\"btn4\">get(过滤方法)</button></div>\n<div><button class=\"btn\" id=\"btn5\">get(获取单实例)</button></div>\n\n<script>\n\n['#demo1', '#demo2', '#xm3'].forEach((el, index) => {\n\txmSelect.render({\n\t\tel, \n\t\tdata: [\n\t\t\t{name: '张三', value: 1, selected: true},\n\t\t\t{name: '李四', value: 2},\n\t\t\t{name: '王五', value: 3, disabled: true},\n\t\t]\n\t});\n});\n\ndocument.getElementById('btn1').onclick = function(){\n\tvar xmList = xmSelect.get();\n\talert('当前页面多选个数: ' + xmList.length)\n}\n\ndocument.getElementById('btn2').onclick = function(){\n\tvar xmList = xmSelect.get('#demo2');\n\talert('获取 #demo2 实例: ' + xmList.length)\n}\n\ndocument.getElementById('btn3').onclick = function(){\n\tvar xmList = xmSelect.get(/demo.*/);\n\talert('获取匹配正则 /demo.*/ 的实例: ' + xmList.length)\n}\n\ndocument.getElementById('btn4').onclick = function(){\n\tvar xmList = xmSelect.get((el) => {\n\t\treturn el == '#demo1' || el == '#xm3'\n\t});\n\talert('自定义方法的实例: ' + xmList.length)\n}\n\ndocument.getElementById('btn5').onclick = function(){\n\tvar demo2 = xmSelect.get('#demo2', true);\n\talert('获取单实例#demo2当前选中值: ' + demo2.getValue('nameStr'));\n}\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}}),t._v(" "),n("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}}),t._v(" "),n("div",{staticClass:"xm-select-demo",attrs:{id:"xm3"}}),t._v(" "),n("div",[n("button",{staticClass:"btn",attrs:{id:"btn1"}},[t._v("get(所有)")])]),t._v(" "),n("div",[n("button",{staticClass:"btn",attrs:{id:"btn2"}},[t._v("get(字符串)")])]),t._v(" "),n("div",[n("button",{staticClass:"btn",attrs:{id:"btn3"}},[t._v("get(正则)")])]),t._v(" "),n("div",[n("button",{staticClass:"btn",attrs:{id:"btn4"}},[t._v("get(过滤方法)")])]),t._v(" "),n("div",[n("button",{staticClass:"btn",attrs:{id:"btn5"}},[t._v("get(获取单实例)")])])])}],mounted:function(){this.$nextTick((function(){["#demo1","#demo2","#xm3"].forEach((function(t,e){xmSelect.render({el:t,data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3,disabled:!0}]})})),document.getElementById("btn1").onclick=function(){var t=xmSelect.get();alert("当前页面多选个数: "+t.length)},document.getElementById("btn2").onclick=function(){var t=xmSelect.get("#demo2");alert("获取 #demo2 实例: "+t.length)},document.getElementById("btn3").onclick=function(){var t=xmSelect.get(/demo.*/);alert("获取匹配正则 /demo.*/ 的实例: "+t.length)},document.getElementById("btn4").onclick=function(){var t=xmSelect.get((function(t){return"#demo1"==t||"#xm3"==t}));alert("自定义方法的实例: "+t.length)},document.getElementById("btn5").onclick=function(){var t=xmSelect.get("#demo2",!0);alert("获取单实例#demo2当前选中值: "+t.getValue("nameStr"))}}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"huo-qu-shi-li-dui-xiang"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#huo-qu-shi-li-dui-xiang"}},[this._v("¶")]),this._v(" 获取实例对象")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"quan-ju-fang-fa-get"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#quan-ju-fang-fa-get"}},[this._v("¶")]),this._v(" 全局方法 get")])},function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"warning"},[e("p",[this._v("get方法默认返回的是符合条件的数组,")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("\n//所有\nxmSelect.get();\n//字符串\nxmSelect.get('#demo2');\n//正则\nxmSelect.get(/demo.*/);\n//过滤方法\nxmSelect.get((el) => {\n\treturn el == '#demo1' || el == '#xm3'\n});\n//获取单实例\nxmSelect.get('#demo2', true);\n")])])}],!1,null,null,null);r.options.__file="docs/mds/ZM07.md";e.default=r.exports},731:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),this._m(2),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n<div id=\"demo2\" class=\"xm-select-demo\"></div>\n<div id=\"xm3\" class=\"xm-select-demo\"></div>\n\n<div><button class=\"btn\" id=\"btn1\">batch禁用</button></div>\n<div><button class=\"btn\" id=\"btn2\">batch启用</button></div>\n<div><button class=\"btn\" id=\"btn3\">batch警告</button></div>\n<div><button class=\"btn\" id=\"btn4\">batch获取已选中数据</button></div>\n\n<pre id=\"demo-value\"></pre>\n\n<script>\n\n['#demo1', '#demo2', '#xm3'].forEach((el, index) => {\n\txmSelect.render({\n\t\tel, \n\t\tdata: [\n\t\t\t{name: '张三', value: 1, selected: true},\n\t\t\t{name: '李四', value: 2},\n\t\t\t{name: '王五', value: 3, disabled: true},\n\t\t]\n\t});\n});\n\ndocument.getElementById('btn1').onclick = function(){\n\txmSelect.batch('', 'update', {\n\t\tdisabled: true\n\t});\n}\n\ndocument.getElementById('btn2').onclick = function(){\n\txmSelect.batch('', 'update', {\n\t\tdisabled: false\n\t});\n}\n\ndocument.getElementById('btn3').onclick = function(){\n\txmSelect.batch(/demo.*/, 'warning', '#F00', true);\n}\n\ndocument.getElementById('btn4').onclick = function(){\n\tvar selectArr = xmSelect.batch(null, 'getValue', 'name');\n\tdocument.getElementById('demo-value').innerHTML = JSON.stringify(selectArr, null, 2);\n}\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}}),t._v(" "),n("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}}),t._v(" "),n("div",{staticClass:"xm-select-demo",attrs:{id:"xm3"}}),t._v(" "),n("div",[n("button",{staticClass:"btn",attrs:{id:"btn1"}},[t._v("batch禁用")])]),t._v(" "),n("div",[n("button",{staticClass:"btn",attrs:{id:"btn2"}},[t._v("batch启用")])]),t._v(" "),n("div",[n("button",{staticClass:"btn",attrs:{id:"btn3"}},[t._v("batch警告")])]),t._v(" "),n("div",[n("button",{staticClass:"btn",attrs:{id:"btn4"}},[t._v("batch获取已选中数据")])]),t._v(" "),n("pre",{attrs:{id:"demo-value"}})])}],mounted:function(){this.$nextTick((function(){["#demo1","#demo2","#xm3"].forEach((function(t,e){xmSelect.render({el:t,data:[{name:"张三",value:1,selected:!0},{name:"李四",value:2},{name:"王五",value:3,disabled:!0}]})})),document.getElementById("btn1").onclick=function(){xmSelect.batch("","update",{disabled:!0})},document.getElementById("btn2").onclick=function(){xmSelect.batch("","update",{disabled:!1})},document.getElementById("btn3").onclick=function(){xmSelect.batch(/demo.*/,"warning","#F00",!0)},document.getElementById("btn4").onclick=function(){var t=xmSelect.batch(null,"getValue","name");document.getElementById("demo-value").innerHTML=JSON.stringify(t,null,2)}}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"pi-liang-cao-zuo"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#pi-liang-cao-zuo"}},[this._v("¶")]),this._v(" 批量操作")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"pi-liang-cao-zuo-yi-xuan-ran-shi-li"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#pi-liang-cao-zuo-yi-xuan-ran-shi-li"}},[this._v("¶")]),this._v(" 批量操作已渲染实例")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("//filter, 同get方法\n//method, 需要执行的方法\n//args, 执行方法的参数\nxmSelect.batch(filter, method, ...args);\n")])])}],!1,null,null,null);r.options.__file="docs/mds/ZM08.md";e.default=r.exports},732:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),e("p",[this._v("至于能干什么, 就看你们的想象了~~")]),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("\n<div id=\"demo1\"></div>\n\n<script>\n//先渲染多选\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tprop: {\n\t\tname: 'username',\n\t\tvalue: 'username',\n\t},\n\tcontent: `\n\t\t<table class=\"layui-table\" lay-filter=\"demo\">\n\t\t  <thead>\n\t\t\t<tr>\n\t\t\t  <th lay-data=\"{field:'username', width:80}\">昵称</th>\n\t\t\t  <th lay-data=\"{field:'experience', width:50, sort:true}\">积分</th>\n\t\t\t  <th lay-data=\"{field:'sign', width: 200}\">签名</th>\n\t\t\t</tr> \n\t\t  </thead>\n\t\t  <tbody>\n\t\t\t<tr>\n\t\t\t  <td>贤心1</td>\n\t\t\t  <td>66</td>\n\t\t\t  <td>人生就像是一场修行a</td>\n\t\t\t</tr>\n\t\t\t<tr>\n\t\t\t  <td>贤心2</td>\n\t\t\t  <td>88</td>\n\t\t\t  <td>人生就像是一场修行b</td>\n\t\t\t</tr>\n\t\t\t<tr>\n\t\t\t  <td>贤心3</td>\n\t\t\t  <td>33</td>\n\t\t\t  <td>人生就像是一场修行c</td>\n\t\t\t</tr>\n\t\t  </tbody>\n\t\t</table>\n\t`,\n\theight: 'auto',\n})\n\nlayui.table.init('demo', {\n\tdone: function(res){\n\t\tdemo1.update({ data: res.data })\n\t}\n}).on('row(demo)', function(obj){\n\tvar values = demo1.getValue();\n\tvar item = obj.data;\n\tvar has = values.find(function(i){\n\t\treturn i.username === item.username\n\t})\n\tif(has){\n\t\tdemo1.delete([ item ]);\n\t}else{\n\t\tdemo1.append([ item ]);\n\t}\n\t\n})\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){var t=xmSelect.render({el:"#demo1",prop:{name:"username",value:"username"},content:'\n\t\t<table class="layui-table" lay-filter="demo">\n\t\t  <thead>\n\t\t\t<tr>\n\t\t\t  <th lay-data="{field:\'username\', width:80}">昵称</th>\n\t\t\t  <th lay-data="{field:\'experience\', width:50, sort:true}">积分</th>\n\t\t\t  <th lay-data="{field:\'sign\', width: 200}">签名</th>\n\t\t\t</tr> \n\t\t  </thead>\n\t\t  <tbody>\n\t\t\t<tr>\n\t\t\t  <td>贤心1</td>\n\t\t\t  <td>66</td>\n\t\t\t  <td>人生就像是一场修行a</td>\n\t\t\t</tr>\n\t\t\t<tr>\n\t\t\t  <td>贤心2</td>\n\t\t\t  <td>88</td>\n\t\t\t  <td>人生就像是一场修行b</td>\n\t\t\t</tr>\n\t\t\t<tr>\n\t\t\t  <td>贤心3</td>\n\t\t\t  <td>33</td>\n\t\t\t  <td>人生就像是一场修行c</td>\n\t\t\t</tr>\n\t\t  </tbody>\n\t\t</table>\n\t',height:"auto"});layui.table.init("demo",{done:function(e){t.update({data:e.data})}}).on("row(demo)",(function(e){var n=t.getValue(),a=e.data;n.find((function(t){return t.username===a.username}))?t.delete([a]):t.append([a])}))}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"xia-la-zi-ding-yi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xia-la-zi-ding-yi"}},[this._v("¶")]),this._v(" 下拉自定义")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"xia-la-biao-ge"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xia-la-biao-ge"}},[this._v("¶")]),this._v(" 下拉表格")])}],!1,null,null,null);r.options.__file="docs/mds/ZP01.md";e.default=r.exports},733:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),n("p",[t._v("默认配置")]),t._m(2),n("demo-block",[n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v('\n<div id="demo1" class="xm-select-demo"></div>\n\n<br/>\n<div class="layui-form">\n\t<input type="checkbox" name="showFolderIcon" lay-filter="showFolderIcon" lay-skin="primary" title="是否展示三角图标" checked>\n\t<input type="checkbox" name="showLine" lay-filter="showLine" lay-skin="primary" title="是否显示虚线" checked>\n\t<input type="checkbox" name="strict" lay-filter="strict" lay-skin="primary" title="严格父子结构" checked>\n\t<input type="checkbox" name="simple" lay-filter="simple" lay-skin="primary" title="极简模式">\n\t\n\t<br/><br/>\n\t<input type="checkbox" name="hidden" lay-filter="hidden" lay-skin="primary" title="隐藏父节点图标">\n\t<input type="checkbox" name="custom" lay-filter="custom" lay-skin="primary" title="自定义图标">\n\t<input type="checkbox" name="all" lay-filter="all" lay-skin="primary" title="展开所有节点">\n\t<input type="checkbox" name="close" lay-filter="close" lay-skin="primary" title="闭合所有节点">\n\t<input type="checkbox" name="key3" lay-filter="key3" lay-skin="primary" title="展开指定节点">\n\t\n\t<br/><br/>\n\t<input type="checkbox" name="clickExpand" lay-filter="clickExpand" lay-skin="primary" title="clickExpand" checked>\n\t<input type="checkbox" name="clickCheck" lay-filter="clickCheck" lay-skin="primary" title="clickExpand" checked>\n\t\n</div>\n\n<div style="margin-top: 20px">间距</div>\n<div id="slideTest1"></div>\n\n\n<script>\nlayui.form.render();\n\n[\'showFolderIcon\', \'showLine\', \'strict\', \'simple\'].forEach(function(key){\n\tlayui.form.on(\'checkbox(\'+key+\')\', function(data){\n\t\tvar treeConfig = {};\n\t\ttreeConfig[key] = data.elem.checked;\n\t\tdemo1.update({\n\t\t\ttree: treeConfig\n\t\t})\n\t});\n})\n\n//控制显示父节点的图标\nlayui.form.on(\'checkbox(hidden)\', function(data){\n\tdemo1.update({\n\t\ticonfont: {\n\t\t\tparent: data.elem.checked ? \'hidden\' : \'\',\n\t\t}\n\t})\n});\n\n//自定义图标\nlayui.form.on(\'checkbox(custom)\', function(data){\n\tlet iconfont = data.elem.checked ? {\n\t\tselect: \'layui-icon layui-icon-chart\',\n\t\tunselect: \'layui-icon-ok-circle\',\n\t\thalf: \'layui-icon layui-icon-table\',\n\t\tparent: \'layui-icon layui-icon-survey\',\n\t} : {\n\t\tselect: \'\',\n\t\tunselect: \'\',\n\t\thalf: \'\',\n\t\tparent: \'\',\n\t}\n\tdemo1.update({\n\t\ticonfont: iconfont\n\t})\n\tlayui.form.render();\n});\n\n//展开所有节点\nlayui.form.on(\'checkbox(all)\', function(data){\n\tif(data.elem.checked){\n\t\tdemo1.changeExpandedKeys(true)\n\t}\n});\n\n//闭合所有节点\nlayui.form.on(\'checkbox(close)\', function(data){\n\tif(data.elem.checked){\n\t\tdemo1.changeExpandedKeys(false)\n\t}\n});\n\n//展开指定节点\nlayui.form.on(\'checkbox(key3)\', function(data){\n\tif(data.elem.checked){\n\t\tdemo1.changeExpandedKeys([ -3 ])\n\t}\n});\n\nlayui.form.on(\'checkbox(clickExpand)\', function(data){\n\tdemo1.update({\n\t\ttree: {\n\t\t\tclickExpand: data.elem.checked\n\t\t}\n\t})\n});\n\nlayui.form.on(\'checkbox(clickCheck)\', function(data){\n\tdemo1.update({\n\t\ttree: {\n\t\t\tclickCheck: data.elem.checked\n\t\t}\n\t})\n});\n\nlayui.slider.render({\n\telem: \'#slideTest1\',\n\tmin: 10,\n\tmax: 100,\n\tshowstep: true,\n\tinput: true,\n\ttips: true,\n\tvalue: 20,\n\tchange: function(value){\n\t\tdemo1.update({\n\t\t\ttree: {\n\t\t\t\tindent: value\n\t\t\t}\n\t\t})\n\t}\n});\n\nvar demo1 = xmSelect.render({\n\tel: \'#demo1\', \n\tautoRow: true,\n\tfilterable: true,\n\ttree: {\n\t\tshow: true,\n\t\tshowFolderIcon: true,\n\t\tshowLine: true,\n\t\tindent: 20,\n\t\texpandedKeys: [ -3 ],\n\t},\n\ttoolbar: {\n\t\tshow: true,\n\t\tlist: [\'ALL\', \'REVERSE\', \'CLEAR\']\n\t},\n\tfilterable: true,\n\theight: \'auto\',\n\tdata: function(){\n\t\treturn [\n\t\t\t{name: \'销售员\', value: -1, disabled: true, children: [\n\t\t\t\t{name: \'张三1\', value: 1, selected: true, children: []},\n\t\t\t\t{name: \'李四1\', value: 2, selected: true},\n\t\t\t\t{name: \'王五1\', value: 3, disabled: true},\n\t\t\t]},\n\t\t\t{name: \'奖品\', value: -2, children: [\n\t\t\t\t{name: \'奖品3\', value: -3, children: [\n\t\t\t\t\t{name: \'苹果3\', value: 14, selected: true},\n\t\t\t\t\t{name: \'香蕉3\', value: 15},\n\t\t\t\t\t{name: \'葡萄3\', value: 16},\n\t\t\t\t]},\n\t\t\t\t{name: \'苹果2\', value: 4, selected: true, disabled: true},\n\t\t\t\t{name: \'香蕉2\', value: 5},\n\t\t\t\t{name: \'葡萄2\', value: 6},\n\t\t\t]},\n\t\t]\n\t}\n})\n<\/script>\n')])])])],2),t._m(3),t._m(4),n("demo-block",[n("template",{slot:"source"},[n("element-demo1")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo2\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo2 = xmSelect.render({\n\tel: '#demo2', \n\tautoRow: true,\n\ttree: {\n\t\tshow: true,\n\t\tshowFolderIcon: true,\n\t\tshowLine: true,\n\t\tindent: 20,\n\t\texpandedKeys: [ -1 ],\n\t\tlazy: true,\n\t\tload: function(item, cb){\n\t\t\tsetTimeout(function(){\n\t\t\t\tif(item.name.endsWith('2')){\n\t\t\t\t\treturn cb([]);\n\t\t\t\t}\n\t\t\t\tcb([\n\t\t\t\t\t{name: item.name + 1, value: item.value + '1', children: [] },\n\t\t\t\t\t{name: item.name + 2, value: item.value + '2', children: [] },\n\t\t\t\t])\n\t\t\t}, 500)\n\t\t}\n\t},\n\theight: 'auto',\n\tdata(){\n\t\treturn [\n\t\t\t{name: '销售员', value: -1, children: [\n\t\t\t\t{name: '张三', value: 100, selected: true, children: []},\n\t\t\t\t{name: '李四1', value: 2, selected: true},\n\t\t\t\t{name: '王五1', value: 3, disabled: true},\n\t\t\t]},\n\t\t\t{name: '奖品', value: -2, children: [\n\t\t\t\t{name: '奖品3', value: -3, children: [\n\t\t\t\t\t{name: '苹果3', value: 14, selected: true},\n\t\t\t\t\t{name: '香蕉3', value: 15},\n\t\t\t\t\t{name: '葡萄3', value: 16},\n\t\t\t\t]},\n\t\t\t\t{name: '苹果2', value: 4, selected: true, disabled: true},\n\t\t\t\t{name: '香蕉2', value: 5},\n\t\t\t\t{name: '葡萄2', value: 6},\n\t\t\t]},\n\t\t]\n\t}\n})\n<\/script>\n")])])])],2),t._m(5),t._m(6),n("demo-block",[n("template",{slot:"source"},[n("element-demo2")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo3\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo3 = xmSelect.render({\n\tel: '#demo3', \n\tmodel: { label: { type: 'text' } },\n\tradio: true,\n\tclickClose: true,\n\ttree: {\n\t\tshow: true,\n\t\tstrict: false,\n\t\texpandedKeys: [ -1 ],\n\t},\n\theight: 'auto',\n\tdata(){\n\t\treturn [\n\t\t\t{name: '销售员', value: -1, children: [\n\t\t\t\t{name: '张三', value: 100, selected: true, children: []},\n\t\t\t\t{name: '李四1', value: 2, selected: true},\n\t\t\t\t{name: '王五1', value: 3, disabled: true},\n\t\t\t]},\n\t\t\t{name: '奖品', value: -2, children: [\n\t\t\t\t{name: '奖品3', value: -3, children: [\n\t\t\t\t\t{name: '苹果3', value: 14, selected: true},\n\t\t\t\t\t{name: '香蕉3', value: 15},\n\t\t\t\t\t{name: '葡萄3', value: 16},\n\t\t\t\t]},\n\t\t\t\t{name: '苹果2', value: 4, selected: true, disabled: true},\n\t\t\t\t{name: '香蕉2', value: 5},\n\t\t\t\t{name: '葡萄2', value: 6},\n\t\t\t]},\n\t\t]\n\t}\n})\n<\/script>\n")])])])],2),t._m(7),t._m(8),n("demo-block",[n("template",{slot:"source"},[n("element-demo3")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo4\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo4 = xmSelect.render({\n\tel: '#demo4', \n\tmodel: { label: { type: 'text' } },\n\ttree: {\n\t\tshow: true,\n\t\tstrict: false,\n\t\texpandedKeys: [ -1 ],\n\t},\n\ton: function(data){\n\t\tif(data.isAdd){\n\t\t\treturn data.change.slice(0, 1)\n\t\t}\n\t},\n\theight: 'auto',\n})\n\n//这里模拟ajax\nsetTimeout(function(){\n\tdemo4.update({\n\t\tdata: [\n\t\t\t{name: '销售员', value: -1, children: [\n\t\t\t\t{name: '张三', value: 100, children: []},\n\t\t\t\t{name: '李四1', value: 2},\n\t\t\t\t{name: '王五1', value: 3, disabled: true},\n\t\t\t]},\n\t\t\t{name: '奖品', value: -2, children: [\n\t\t\t\t{name: '奖品3', value: -3, children: [\n\t\t\t\t\t{name: '苹果3', value: 14},\n\t\t\t\t\t{name: '香蕉3', value: 15},\n\t\t\t\t\t{name: '葡萄3', value: 16},\n\t\t\t\t]},\n\t\t\t\t{name: '苹果2', value: 4, disabled: true},\n\t\t\t\t{name: '香蕉2', value: 5},\n\t\t\t\t{name: '葡萄2', value: 6},\n\t\t\t]},\n\t\t]\n\t})\n\t\n\t//设置默认值\n\tdemo4.setValue([\n\t\t{name: '李四1', value: 2},\n\t\t{name: '苹果3', value: 14},\n\t], null, true)\n\t\n}, 300)\n\n<\/script>\n")])])])],2),t._m(9),t._m(10),n("demo-block",[n("template",{slot:"source"},[n("element-demo4")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo5\" class=\"xm-select-demo\"></div>\n\n<script>\nvar demo5 = xmSelect.render({\n\tel: '#demo5', \n\ttree: {\n\t\tshow: true,\n\t\texpandedKeys: true,\n\t},\n\theight: 'auto',\n\tdata: [\n\t\t{name: '销售员', value: -1, children: [\n\t\t\t{name: '张三', value: 100, children: []},\n\t\t\t{name: '李四1', value: 2},\n\t\t\t{name: '王五1', value: 3, disabled: true},\n\t\t]},\n\t\t{name: '奖品', value: -2, children: [\n\t\t\t{name: '奖品3', value: -3, children: [\n\t\t\t\t{name: '苹果3', value: 14},\n\t\t\t\t{name: '香蕉3', value: 15},\n\t\t\t\t{name: '葡萄3', value: 16},\n\t\t\t]},\n\t\t\t{name: '苹果2', value: 4, disabled: true},\n\t\t\t{name: '香蕉2', value: 5},\n\t\t\t{name: '葡萄2', value: 6},\n\t\t]},\n\t]\n})\n\n<\/script>\n")])])])],2)],1)};function i(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}a._withStripped=!0;var s={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}}),t._v(" "),n("br"),t._v(" "),n("div",{staticClass:"layui-form"},[n("input",{attrs:{type:"checkbox",name:"showFolderIcon","lay-filter":"showFolderIcon","lay-skin":"primary",title:"是否展示三角图标",checked:""}}),t._v(" "),n("input",{attrs:{type:"checkbox",name:"showLine","lay-filter":"showLine","lay-skin":"primary",title:"是否显示虚线",checked:""}}),t._v(" "),n("input",{attrs:{type:"checkbox",name:"strict","lay-filter":"strict","lay-skin":"primary",title:"严格父子结构",checked:""}}),t._v(" "),n("input",{attrs:{type:"checkbox",name:"simple","lay-filter":"simple","lay-skin":"primary",title:"极简模式"}}),t._v(" "),n("br"),n("br"),t._v(" "),n("input",{attrs:{type:"checkbox",name:"hidden","lay-filter":"hidden","lay-skin":"primary",title:"隐藏父节点图标"}}),t._v(" "),n("input",{attrs:{type:"checkbox",name:"custom","lay-filter":"custom","lay-skin":"primary",title:"自定义图标"}}),t._v(" "),n("input",{attrs:{type:"checkbox",name:"all","lay-filter":"all","lay-skin":"primary",title:"展开所有节点"}}),t._v(" "),n("input",{attrs:{type:"checkbox",name:"close","lay-filter":"close","lay-skin":"primary",title:"闭合所有节点"}}),t._v(" "),n("input",{attrs:{type:"checkbox",name:"key3","lay-filter":"key3","lay-skin":"primary",title:"展开指定节点"}}),t._v(" "),n("br"),n("br"),t._v(" "),n("input",{attrs:{type:"checkbox",name:"clickExpand","lay-filter":"clickExpand","lay-skin":"primary",title:"clickExpand",checked:""}}),t._v(" "),n("input",{attrs:{type:"checkbox",name:"clickCheck","lay-filter":"clickCheck","lay-skin":"primary",title:"clickExpand",checked:""}})]),t._v(" "),n("div",{staticStyle:{"margin-top":"20px"}},[t._v("间距")]),t._v(" "),n("div",{attrs:{id:"slideTest1"}})])}],mounted:function(){this.$nextTick((function(){var t;layui.form.render(),["showFolderIcon","showLine","strict","simple"].forEach((function(t){layui.form.on("checkbox("+t+")",(function(n){var a={};a[t]=n.elem.checked,e.update({tree:a})}))})),layui.form.on("checkbox(hidden)",(function(t){e.update({iconfont:{parent:t.elem.checked?"hidden":""}})})),layui.form.on("checkbox(custom)",(function(t){var n=t.elem.checked?{select:"layui-icon layui-icon-chart",unselect:"layui-icon-ok-circle",half:"layui-icon layui-icon-table",parent:"layui-icon layui-icon-survey"}:{select:"",unselect:"",half:"",parent:""};e.update({iconfont:n}),layui.form.render()})),layui.form.on("checkbox(all)",(function(t){t.elem.checked&&e.changeExpandedKeys(!0)})),layui.form.on("checkbox(close)",(function(t){t.elem.checked&&e.changeExpandedKeys(!1)})),layui.form.on("checkbox(key3)",(function(t){t.elem.checked&&e.changeExpandedKeys([-3])})),layui.form.on("checkbox(clickExpand)",(function(t){e.update({tree:{clickExpand:t.elem.checked}})})),layui.form.on("checkbox(clickCheck)",(function(t){e.update({tree:{clickCheck:t.elem.checked}})})),layui.slider.render({elem:"#slideTest1",min:10,max:100,showstep:!0,input:!0,tips:!0,value:20,change:function(t){e.update({tree:{indent:t}})}});var e=xmSelect.render((i(t={el:"#demo1",autoRow:!0,filterable:!0,tree:{show:!0,showFolderIcon:!0,showLine:!0,indent:20,expandedKeys:[-3]},toolbar:{show:!0,list:["ALL","REVERSE","CLEAR"]}},"filterable",!0),i(t,"height","auto"),i(t,"data",(function(){return[{name:"销售员",value:-1,disabled:!0,children:[{name:"张三1",value:1,selected:!0,children:[]},{name:"李四1",value:2,selected:!0},{name:"王五1",value:3,disabled:!0}]},{name:"奖品",value:-2,children:[{name:"奖品3",value:-3,children:[{name:"苹果3",value:14,selected:!0},{name:"香蕉3",value:15},{name:"葡萄3",value:16}]},{name:"苹果2",value:4,selected:!0,disabled:!0},{name:"香蕉2",value:5},{name:"葡萄2",value:6}]}]})),t))}))}}}(),"element-demo1":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo2"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo2",autoRow:!0,tree:{show:!0,showFolderIcon:!0,showLine:!0,indent:20,expandedKeys:[-1],lazy:!0,load:function(t,e){setTimeout((function(){if(t.name.endsWith("2"))return e([]);e([{name:t.name+1,value:t.value+"1",children:[]},{name:t.name+2,value:t.value+"2",children:[]}])}),500)}},height:"auto",data:function(){return[{name:"销售员",value:-1,children:[{name:"张三",value:100,selected:!0,children:[]},{name:"李四1",value:2,selected:!0},{name:"王五1",value:3,disabled:!0}]},{name:"奖品",value:-2,children:[{name:"奖品3",value:-3,children:[{name:"苹果3",value:14,selected:!0},{name:"香蕉3",value:15},{name:"葡萄3",value:16}]},{name:"苹果2",value:4,selected:!0,disabled:!0},{name:"香蕉2",value:5},{name:"葡萄2",value:6}]}]}})}))}}}(),"element-demo2":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo3"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo3",model:{label:{type:"text"}},radio:!0,clickClose:!0,tree:{show:!0,strict:!1,expandedKeys:[-1]},height:"auto",data:function(){return[{name:"销售员",value:-1,children:[{name:"张三",value:100,selected:!0,children:[]},{name:"李四1",value:2,selected:!0},{name:"王五1",value:3,disabled:!0}]},{name:"奖品",value:-2,children:[{name:"奖品3",value:-3,children:[{name:"苹果3",value:14,selected:!0},{name:"香蕉3",value:15},{name:"葡萄3",value:16}]},{name:"苹果2",value:4,selected:!0,disabled:!0},{name:"香蕉2",value:5},{name:"葡萄2",value:6}]}]}})}))}}}(),"element-demo3":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo4"}})])}],mounted:function(){this.$nextTick((function(){var t=xmSelect.render({el:"#demo4",model:{label:{type:"text"}},tree:{show:!0,strict:!1,expandedKeys:[-1]},on:function(t){if(t.isAdd)return t.change.slice(0,1)},height:"auto"});setTimeout((function(){t.update({data:[{name:"销售员",value:-1,children:[{name:"张三",value:100,children:[]},{name:"李四1",value:2},{name:"王五1",value:3,disabled:!0}]},{name:"奖品",value:-2,children:[{name:"奖品3",value:-3,children:[{name:"苹果3",value:14},{name:"香蕉3",value:15},{name:"葡萄3",value:16}]},{name:"苹果2",value:4,disabled:!0},{name:"香蕉2",value:5},{name:"葡萄2",value:6}]}]}),t.setValue([{name:"李四1",value:2},{name:"苹果3",value:14}],null,!0)}),300)}))}}}(),"element-demo4":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo5"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo5",tree:{show:!0,expandedKeys:!0},height:"auto",data:[{name:"销售员",value:-1,children:[{name:"张三",value:100,children:[]},{name:"李四1",value:2},{name:"王五1",value:3,disabled:!0}]},{name:"奖品",value:-2,children:[{name:"奖品3",value:-3,children:[{name:"苹果3",value:14},{name:"香蕉3",value:15},{name:"葡萄3",value:16}]},{name:"苹果2",value:4,disabled:!0},{name:"香蕉2",value:5},{name:"葡萄2",value:6}]}]})}))}}}()}},r=n(26),l=Object(r.a)(s,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"xia-la-shu"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xia-la-shu"}},[this._v("¶")]),this._v(" 下拉树")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"tree"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#tree"}},[this._v("¶")]),this._v(" tree")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("tree: {\n\t//是否显示树状结构\n\tshow: true,\n\t//是否展示三角图标\n\tshowFolderIcon: true,\n\t//是否显示虚线\n\tshowLine: false,\n\t//间距\n\tindent: 20,\n\t//默认展开节点的数组, 为 true 时, 展开所有节点\n\texpandedKeys: [], \n\t//是否严格遵守父子模式\n\tstrict: true,\n\t//是否开启极简模式\n\tsimple: false,\n\t//点击节点是否展开\n\tclickExpand: true,\n\t//点击节点是否选中\n\tclickCheck: true,\n},\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"lan-jia-zai-de-shu"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#lan-jia-zai-de-shu"}},[this._v("¶")]),this._v(" 懒加载的树")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("tree: {\n\tshow: true,\n\texpandedKeys: [ -1 ],\n\t//开启懒加载\n\tlazy: true,\n\t//加载方法\n\tload: function(item, cb){\n\t\t//item: 点击的节点, cb: 回调函数\n\t\t//这里模拟ajax\n\t\tsetTimeout(function(){\n\t\t\tvar name = item.name + 1;\n\t\t\tcb([\n\t\t\t\t{name: item.name + 1, value: item.value + '1', children: [] },\n\t\t\t\t{name: item.name + 2, value: item.value + '2' },\n\t\t\t])\n\t\t}, 500)\n\t}\n},\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"dan-xuan-shu-radio-mo-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#dan-xuan-shu-radio-mo-shi"}},[this._v("¶")]),this._v(" 单选树(radio模式)")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("//显示为text模式\nmodel: { label: { type: 'text' } },\n//单选模式\nradio: true,\n//选中关闭\nclickClose: true,\n//树\ntree: {\n\tshow: true,\n\t//非严格模式\n\tstrict: false,\n\t//默认展开节点\n\texpandedKeys: [ -1 ],\n},\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"dan-xuan-shu-on-chu-li-mo-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#dan-xuan-shu-on-chu-li-mo-shi"}},[this._v("¶")]),this._v(" 单选树(on处理模式)")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("//显示为text模式\nmodel: { label: { type: 'text' } },\n//树\ntree: {\n\tshow: true,\n\t//非严格模式\n\tstrict: false,\n\t//默认展开节点\n\texpandedKeys: [ -1, -3 ],\n},\n//处理方式\non: function(data){\n\tif(data.isAdd){\n\t\treturn data.change.slice(0, 1)\n\t}\n},\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"mo-ren-zhan-kai-suo-you-jie-dian"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#mo-ren-zhan-kai-suo-you-jie-dian"}},[this._v("¶")]),this._v(" 默认展开所有节点")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("tree: {\n\tshow: true,\n\t//展开所有节点\n\texpandedKeys: true,\n},\n")])])}],!1,null,null,null);l.options.__file="docs/mds/ZP02.md";e.default=l.exports},734:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),this._m(2),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("\n<div id=\"demo1\" style=\"width: 274px;\"></div>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tcontent: '<div id=\"laydate\" />',\n\theight: 'auto',\n\tautoRow: true,\n\ton: function(data){\n\t\tif(!data.isAdd){\n\t\t\tdateSelect(demo1.getValue('value'));\n\t\t}\n\t}\n})\n\nlayui.laydate.render({\n\telem: '#laydate',\n\tposition: 'static',\n\tshowBottom: false,\n\tformat: 'yyyy-M-dd',\n\tchange: function(){\n\t\tdateSelect(demo1.getValue('value'));\n\t},\n\tdone: function(value){\n\t\tconsole.log(value)\n\t\tvar values = demo1.getValue('value');\n\t\tvar index = values.findIndex(function(val){\n\t\t\treturn val === value\n\t\t});\n\t\t\n\t\tif(index != -1){\n\t\t\tvalues.splice(index, 1);\n\t\t}else{\n\t\t\tvalues.push(value);\n\t\t}\n\t\t\n\t\tdateSelect(values);\n\t\t\n\t\tdemo1.update({\n\t\t\tdata: values.map(function(val){\n\t\t\t\treturn {\n\t\t\t\t\tname: val,\n\t\t\t\t\tvalue: val,\n\t\t\t\t\tselected: true,\n\t\t\t\t}\n\t\t\t})\n\t\t})\n\t},\n\tready: removeAll,\n})\n\nfunction removeAll(){\n\tdocument.querySelectorAll('#laydate td[lay-ymd].layui-this').forEach(function(dom){\n\t\tdom.classList.remove('layui-this');\n\t});\n}\n\nfunction dateSelect(values){\n\tremoveAll();\n\tvalues.forEach(function(val){\n\t\tvar dom = document.querySelector('#laydate td[lay-ymd=\"'+val.replace(/-0([1-9])/g, '-$1')+'\"]');\n\t\tdom && dom.classList.add('layui-this');\n\t});\n}\n\n//这里仅仅提供一个演示, 更多的想法由你自己来创造\n\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticStyle:{width:"274px"},attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){var t=xmSelect.render({el:"#demo1",content:'<div id="laydate" />',height:"auto",autoRow:!0,on:function(e){e.isAdd||n(t.getValue("value"))}});function e(){document.querySelectorAll("#laydate td[lay-ymd].layui-this").forEach((function(t){t.classList.remove("layui-this")}))}function n(t){e(),t.forEach((function(t){var e=document.querySelector('#laydate td[lay-ymd="'+t.replace(/-0([1-9])/g,"-$1")+'"]');e&&e.classList.add("layui-this")}))}layui.laydate.render({elem:"#laydate",position:"static",showBottom:!1,format:"yyyy-M-dd",change:function(){n(t.getValue("value"))},done:function(e){console.log(e);var a=t.getValue("value"),i=a.findIndex((function(t){return t===e}));-1!=i?a.splice(i,1):a.push(e),n(a),t.update({data:a.map((function(t){return{name:t,value:t,selected:!0}}))})},ready:e})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"xia-la-ri-qi-duo-xuan"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xia-la-ri-qi-duo-xuan"}},[this._v("¶")]),this._v(" 下拉日期多选")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"laydate"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#laydate"}},[this._v("¶")]),this._v(" laydate")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("//css调整部分\nxm-select .scroll-body{\n\tmargin-top: -5px;\n}\nxm-select .xm-body{\n\tpadding: 0;\n\tborder: none;\n\tbackground-color: usnet;\n\tbox-shadow: none;\n}\n")])])}],!1,null,null,null);r.options.__file="docs/mds/ZP03.md";e.default=r.exports},735:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),this._m(2),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("\n<div id=\"demo1\" style=\"width: 500px;\"></div>\n\n<script>\n\nvar list = [\n\t{ name: '杜甫', selected: true, content: '杜甫的思想核心是儒家的仁政思想，他有“致君尧舜上，再使风俗淳”的宏伟抱负。杜甫虽然在世时名声并不显赫，但后来声名远播，对中国文学和日本文学都产生了深远的影响。杜甫共有约1500首诗歌被保留了下来，大多集于《杜工部集》。' },\n\t{ name: '李清照', content: '李清照出生于书香门第，早期生活优裕，其父李格非藏书甚富，她小时候就在良好的家庭环境中打下文学基础。出嫁后与夫赵明诚共同致力于书画金石的搜集整理。金兵入据中原时，流寓南方，境遇孤苦。所作词，前期多写其悠闲生活，后期多悲叹身世，情调感伤。形式上善用白描手法，自辟途径，语言清丽。' },\n\t{ name: '鲁迅', content: '鲁迅一生在文学创作、文学批评、思想研究、文学史研究、翻译、美术理论引进、基础科学介绍和古籍校勘与研究等多个领域具有重大贡献。他对于五四运动以后的中国社会思想文化发展具有重大影响，蜚声世界文坛，尤其在韩国、日本思想文化领域有极其重要的地位和影响，被誉为“二十世纪东亚文化地图上占最大领土的作家”。' },\n]\n\nvar html = list.map(function(item){\n\treturn `\n\t\t<div class=\"layui-colla-item\">\n\t\t\t<h2 class=\"layui-colla-title\" value=\"${item.name}\">${ item.name }</h2>\n\t\t\t<div class=\"layui-colla-content ${ item.selected && 'layui-show' }\">${ item.content }</div>\n\t\t</div>\n\t`\n}).join('');\n\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tcontent: `\n\t\t<div class=\"layui-collapse\" lay-filter=\"collapse\">\n\t\t\t${ html }\n\t\t</div>\n\t`,\n\theight: 'auto',\n\tautoRow: true,\n\tdata: list,\n\tprop: { value: 'name' },\n\ton: function(data){\n\t\tif(!data.isAdd){\n\t\t\tvar item = data.change[0];\n\t\t\tdocument.querySelector(`#demo1 .layui-collapse .layui-colla-title[value=\"${item.name}\"]`).nextElementSibling.classList.remove('layui-show')\n\t\t}\n\t}\n})\n\nlayui.element.init()\n\nlayui.element.on('collapse(collapse)', function(data){\n\tlet value = data.title.attr('value');\n\tif(data.show){\n\t\tdemo1.append([ value ])\n\t}else{\n\t\tdemo1.delete([ value ])\n\t}\n});\n\n//这里仅仅提供一个演示, 更多的想法由你自己来创造\n\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticStyle:{width:"500px"},attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){var t=[{name:"杜甫",selected:!0,content:"杜甫的思想核心是儒家的仁政思想，他有“致君尧舜上，再使风俗淳”的宏伟抱负。杜甫虽然在世时名声并不显赫，但后来声名远播，对中国文学和日本文学都产生了深远的影响。杜甫共有约1500首诗歌被保留了下来，大多集于《杜工部集》。"},{name:"李清照",content:"李清照出生于书香门第，早期生活优裕，其父李格非藏书甚富，她小时候就在良好的家庭环境中打下文学基础。出嫁后与夫赵明诚共同致力于书画金石的搜集整理。金兵入据中原时，流寓南方，境遇孤苦。所作词，前期多写其悠闲生活，后期多悲叹身世，情调感伤。形式上善用白描手法，自辟途径，语言清丽。"},{name:"鲁迅",content:"鲁迅一生在文学创作、文学批评、思想研究、文学史研究、翻译、美术理论引进、基础科学介绍和古籍校勘与研究等多个领域具有重大贡献。他对于五四运动以后的中国社会思想文化发展具有重大影响，蜚声世界文坛，尤其在韩国、日本思想文化领域有极其重要的地位和影响，被誉为“二十世纪东亚文化地图上占最大领土的作家”。"}],e=t.map((function(t){return'\n\t\t<div class="layui-colla-item">\n\t\t\t<h2 class="layui-colla-title" value="'.concat(t.name,'">').concat(t.name,'</h2>\n\t\t\t<div class="layui-colla-content ').concat(t.selected&&"layui-show",'">').concat(t.content,"</div>\n\t\t</div>\n\t")})).join(""),n=xmSelect.render({el:"#demo1",content:'\n\t\t<div class="layui-collapse" lay-filter="collapse">\n\t\t\t'.concat(e,"\n\t\t</div>\n\t"),height:"auto",autoRow:!0,data:t,prop:{value:"name"},on:function(t){if(!t.isAdd){var e=t.change[0];document.querySelector('#demo1 .layui-collapse .layui-colla-title[value="'.concat(e.name,'"]')).nextElementSibling.classList.remove("layui-show")}}});layui.element.init(),layui.element.on("collapse(collapse)",(function(t){var e=t.title.attr("value");t.show?n.append([e]):n.delete([e])}))}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"xia-la-zhe-die-mian-ban"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xia-la-zhe-die-mian-ban"}},[this._v("¶")]),this._v(" 下拉折叠面板")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"layui-zhe-die-mian-ban"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#layui-zhe-die-mian-ban"}},[this._v("¶")]),this._v(" layui折叠面板")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("//css调整部分\nxm-select .scroll-body{\n\tmargin-top: -5px;\n}\nxm-select .xm-body{\n\tpadding: 0;\n\tborder: none;\n\tbackground-color: usnet;\n\tbox-shadow: none;\n}\n")])])}],!1,null,null,null);r.options.__file="docs/mds/ZP04.md";e.default=r.exports},736:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),this._m(2),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("\n<div id=\"demo1\" style=\"width: 502px;\"></div>\n\n<script>\n\nvar list = [\n\t{value: \"1\", title: \"李白\" },\n\t{value: \"2\", title: \"杜甫\" },\n\t{value: \"3\", title: \"贤心\" },\n]\n\nvar initValue = [\"1\"]\n\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tcontent: \"<div id='transfer'></div>\",\n\theight: 'auto',\n\tautoRow: true,\n\tinitValue: initValue,\n\tdata: list,\n\tprop: { name: 'title' },\n\ton: function(data){\n\t\tif(!data.isAdd){\n\t\t\tlayui.transfer.reload('transfer', {\n\t\t\t\tvalue: demo1.getValue('value')\n\t\t\t})\n\t\t}\n\t}\n})\n\nlayui.transfer.render({\n\tid: 'transfer',\n\telem: '#transfer',  //绑定元素\n\ttitle: ['候选人', '获奖者'],\n\tdata: list,\n\tvalue: initValue,\n\tonchange: function(data, index){\n\t\tif(index == 0){\n\t\t\tdemo1.append(data)\n\t\t}else{\n\t\t\tdemo1.delete(data)\n\t\t}\n\t}\n});\n\n\n//这里仅仅提供一个演示, 更多的想法由你自己来创造\n\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticStyle:{width:"502px"},attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){var t=[{value:"1",title:"李白"},{value:"2",title:"杜甫"},{value:"3",title:"贤心"}],e=["1"],n=xmSelect.render({el:"#demo1",content:"<div id='transfer'></div>",height:"auto",autoRow:!0,initValue:e,data:t,prop:{name:"title"},on:function(t){t.isAdd||layui.transfer.reload("transfer",{value:n.getValue("value")})}});layui.transfer.render({id:"transfer",elem:"#transfer",title:["候选人","获奖者"],data:t,value:e,onchange:function(t,e){0==e?n.append(t):n.delete(t)}})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"xia-la-chuan-suo-kuang"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xia-la-chuan-suo-kuang"}},[this._v("¶")]),this._v(" 下拉穿梭框")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"layui-transfer"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#layui-transfer"}},[this._v("¶")]),this._v(" layui transfer")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("//css调整部分\nxm-select .scroll-body{\n\tpadding-left: 10px;\n\toverflow: hidden;\n}\n")])])}],!1,null,null,null);r.options.__file="docs/mds/ZP05.md";e.default=r.exports},737:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),this._m(1),e("p",[this._v("默认配置")]),this._m(2),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("\n<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<br/>\n<div class=\"layui-form\">\n\t<input type=\"checkbox\" name=\"strict\" lay-filter=\"strict\" lay-skin=\"primary\" title=\"严格父子结构\" checked>\n\t<input type=\"checkbox\" name=\"hidden\" lay-filter=\"hidden\" lay-skin=\"primary\" title=\"隐藏父节点图标\">\n\t<input type=\"checkbox\" name=\"custom\" lay-filter=\"custom\" lay-skin=\"primary\" title=\"自定义图标\">\n</div>\n\n<div style=\"margin-top: 20px\">间距</div>\n<div id=\"slideTest1\"></div>\n\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tautoRow: true,\n\tcascader: {\n\t\tshow: true,\n\t\tindent: 200,\n\t},\n\theight: '200px',\n\tdata(){\n\t\treturn [\n\t\t\t{name: '销售员', value: -1, disabled: true, children: [\n\t\t\t\t{name: '张三1', value: 1, selected: true, children: []},\n\t\t\t\t{name: '李四1', value: 2, selected: true},\n\t\t\t\t{name: '王五1', value: 3, disabled: true},\n\t\t\t]},\n\t\t\t{name: '奖品', value: -2, children: [\n\t\t\t\t{name: '奖品3', value: -3, children: [\n\t\t\t\t\t{name: '苹果3', value: 14, selected: true},\n\t\t\t\t\t{name: '香蕉3', value: 15},\n\t\t\t\t\t{name: '葡萄3', value: 16},\n\t\t\t\t]},\n\t\t\t\t{name: '苹果2', value: 4, selected: true, disabled: true},\n\t\t\t\t{name: '香蕉2', value: 5},\n\t\t\t\t{name: '葡萄2', value: 6},\n\t\t\t]},\n\t\t]\n\t}\n})\n\nlayui.form.render();\n\n['strict'].forEach(function(key){\n\tlayui.form.on('checkbox('+key+')', function(data){\n\t\tvar config = {};\n\t\tconfig[key] = data.elem.checked;\n\t\tdemo1.update({\n\t\t\tcascader: config\n\t\t})\n\t});\n})\n\n//控制显示父节点的图标\nlayui.form.on('checkbox(hidden)', function(data){\n\tdemo1.update({\n\t\ticonfont: {\n\t\t\tparent: data.elem.checked ? 'hidden' : '',\n\t\t}\n\t})\n});\n//自定义图标\nlayui.form.on('checkbox(custom)', function(data){\n\tlet iconfont = data.elem.checked ? {\n\t\tselect: 'layui-icon layui-icon-chart',\n\t\tunselect: 'layui-icon-ok-circle',\n\t\thalf: 'layui-icon layui-icon-table',\n\t\tparent: 'layui-icon layui-icon-survey',\n\t} : {\n\t\tselect: '',\n\t\tunselect: '',\n\t\thalf: '',\n\t\tparent: '',\n\t}\n\tdemo1.update({\n\t\ticonfont: iconfont\n\t})\n\tlayui.form.render();\n});\n\n\nlayui.slider.render({\n\telem: '#slideTest1',\n\tmin: 50,\n\tmax: 400,\n\tshowstep: true,\n\tinput: true,\n\ttips: true,\n\tvalue: 200,\n\tchange: function(value){\n\t\tdemo1.update({\n\t\t\tcascader: {\n\t\t\t\tindent: value\n\t\t\t}\n\t\t})\n\t}\n});\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}}),this._v(" "),e("br"),this._v(" "),e("div",{staticClass:"layui-form"},[e("input",{attrs:{type:"checkbox",name:"strict","lay-filter":"strict","lay-skin":"primary",title:"严格父子结构",checked:""}}),this._v(" "),e("input",{attrs:{type:"checkbox",name:"hidden","lay-filter":"hidden","lay-skin":"primary",title:"隐藏父节点图标"}}),this._v(" "),e("input",{attrs:{type:"checkbox",name:"custom","lay-filter":"custom","lay-skin":"primary",title:"自定义图标"}})]),this._v(" "),e("div",{staticStyle:{"margin-top":"20px"}},[this._v("间距")]),this._v(" "),e("div",{attrs:{id:"slideTest1"}})])}],mounted:function(){this.$nextTick((function(){var t=xmSelect.render({el:"#demo1",autoRow:!0,cascader:{show:!0,indent:200},height:"200px",data:function(){return[{name:"销售员",value:-1,disabled:!0,children:[{name:"张三1",value:1,selected:!0,children:[]},{name:"李四1",value:2,selected:!0},{name:"王五1",value:3,disabled:!0}]},{name:"奖品",value:-2,children:[{name:"奖品3",value:-3,children:[{name:"苹果3",value:14,selected:!0},{name:"香蕉3",value:15},{name:"葡萄3",value:16}]},{name:"苹果2",value:4,selected:!0,disabled:!0},{name:"香蕉2",value:5},{name:"葡萄2",value:6}]}]}});layui.form.render(),["strict"].forEach((function(e){layui.form.on("checkbox("+e+")",(function(n){var a={};a[e]=n.elem.checked,t.update({cascader:a})}))})),layui.form.on("checkbox(hidden)",(function(e){t.update({iconfont:{parent:e.elem.checked?"hidden":""}})})),layui.form.on("checkbox(custom)",(function(e){var n=e.elem.checked?{select:"layui-icon layui-icon-chart",unselect:"layui-icon-ok-circle",half:"layui-icon layui-icon-table",parent:"layui-icon layui-icon-survey"}:{select:"",unselect:"",half:"",parent:""};t.update({iconfont:n}),layui.form.render()})),layui.slider.render({elem:"#slideTest1",min:50,max:400,showstep:!0,input:!0,tips:!0,value:200,change:function(e){t.update({cascader:{indent:e}})}})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"ji-lian-mo-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#ji-lian-mo-shi"}},[this._v("¶")]),this._v(" 级联模式")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"cascader"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#cascader"}},[this._v("¶")]),this._v(" cascader")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("cascader: {\n\t//是否显示级联模式\n\tshow: true,\n\t//间距\n\tindent: 200,\n\t//是否严格遵守父子模式\n\tstrict: true,\n},\n")])])}],!1,null,null,null);r.options.__file="docs/mds/ZP06.md";e.default=r.exports},738:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement,e=this._self._c||t;return e("section",{staticClass:"content element-doc"},[this._m(0),e("demo-block",[e("template",{slot:"source"},[e("element-demo0")],1),e("template",{slot:"highlight"},[e("pre",{pre:!0},[e("code",{pre:!0,attrs:{class:"html"}},[this._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n\n<script>\nxmSelect.render({\n\tel: '#demo1', \n\tautoRow: true,\n\ttree: {\n\t\tshow: true,\n\t},\n\theight: '200px',\n\tmaxMethod(a, item){\n\t\tconsole.log(item)\n\t},\n\tsubmitConversion(sels, prop){\n\t\treturn sels.map(item => item[prop.name]).join(',')\n\t},\n\tfilterable: true,\n\tdata(){\n\t\treturn [\n\t\t\t{name: '销售员', value: -1, disabled: false, children: [\n\t\t\t\t{name: '张三1', value: 1, selected: true, children: []},\n\t\t\t\t{name: '王五1', value: 13, disabled: true},\n\t\t\t\t{name: '王五1', value: 131, disabled: true},\n\t\t\t\t{name: '王五1', value: 132, disabled: true},\n\t\t\t\t{name: '王五1', value: 133, disabled: true},\n\t\t\t\t{name: '王五1', value: 134, disabled: true},\n\t\t\t\t{name: '王五1', value: 135, disabled: true},\n\t\t\t\t{name: '王五1', value: 136, disabled: true},\n\t\t\t\t{name: '王五1', value: 137, disabled: true},\n\t\t\t\t{name: '王五1', value: 138, disabled: true},\n\t\t\t]},\n\t\t\t{name: '奖品', value: -2, children: [\n\t\t\t\t{name: '奖品3', value: -3, children: [\n\t\t\t\t\t\n\t\t\t\t]},\n\t\t\t\t{name: '苹果2', value: 4, disabled: true},\n\t\t\t\t{name: '香蕉2', value: 5},\n\t\t\t\t{name: '葡萄2', value: 6},\n\t\t\t]},\n\t\t\t{name: '李四1', value: 2},\n\t\t\t{name: '王五1', value: 3, disabled: true},\n\t\t\t{name: '王五1', value: 31, disabled: true},\n\t\t\t{name: '王五1', value: 32, disabled: true},\n\t\t\t{name: '王五1', value: 33, disabled: true},\n\t\t\t{name: '王五1', value: 34, disabled: true},\n\t\t\t{name: '王五1', value: 35, disabled: true},\n\t\t\t{name: '王五1', value: 36, disabled: true},\n\t\t\t{name: '王五1', value: 37, disabled: true},\n\t\t\t{name: '王五1', value: 38, disabled: true},\n\t\t]\n\t}\n\t\n})\n\n<\/script>\n")])])])],2)],1)};a._withStripped=!0;var i={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}})])}],mounted:function(){this.$nextTick((function(){xmSelect.render({el:"#demo1",autoRow:!0,tree:{show:!0},height:"200px",maxMethod:function(t,e){console.log(e)},submitConversion:function(t,e){return t.map((function(t){return t[e.name]})).join(",")},filterable:!0,data:function(){return[{name:"销售员",value:-1,disabled:!1,children:[{name:"张三1",value:1,selected:!0,children:[]},{name:"王五1",value:13,disabled:!0},{name:"王五1",value:131,disabled:!0},{name:"王五1",value:132,disabled:!0},{name:"王五1",value:133,disabled:!0},{name:"王五1",value:134,disabled:!0},{name:"王五1",value:135,disabled:!0},{name:"王五1",value:136,disabled:!0},{name:"王五1",value:137,disabled:!0},{name:"王五1",value:138,disabled:!0}]},{name:"奖品",value:-2,children:[{name:"奖品3",value:-3,children:[]},{name:"苹果2",value:4,disabled:!0},{name:"香蕉2",value:5},{name:"葡萄2",value:6}]},{name:"李四1",value:2},{name:"王五1",value:3,disabled:!0},{name:"王五1",value:31,disabled:!0},{name:"王五1",value:32,disabled:!0},{name:"王五1",value:33,disabled:!0},{name:"王五1",value:34,disabled:!0},{name:"王五1",value:35,disabled:!0},{name:"王五1",value:36,disabled:!0},{name:"王五1",value:37,disabled:!0},{name:"王五1",value:38,disabled:!0}]}})}))}}}()}},s=n(26),r=Object(s.a)(i,a,[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"ce-shi"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#ce-shi"}},[this._v("¶")]),this._v(" 测试")])}],!1,null,null,null);r.options.__file="docs/mds/ZTEST.md";e.default=r.exports},739:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[t._m(0),t._m(1),t._m(2),t._m(3),t._m(4),n("p",[t._v("maplemei, 热爱前端的Java程序猿, 如果喜欢作者的插件, 可以请作者吃雪糕 ^_^")]),t._m(5),n("p",[t._v("喜欢就支持一下吧")]),t._m(6),t._m(7),t._m(8),t._m(9),n("p",[t._v("有兴趣的小伙伴可以从git上下载源码进行二次开发")]),t._m(10),t._m(11),n("p",[t._v("一个简单的小栗子，看看如何使用xm-select.js")]),t._m(12),n("demo-block",[n("div",[n("p",[t._v("只需引入"),n("code",[t._v("xm-select.js")])])]),n("template",{slot:"source"},[n("element-demo0")],1),n("template",{slot:"highlight"},[n("pre",{pre:!0},[n("code",{pre:!0,attrs:{class:"html"}},[t._v("<div id=\"demo1\" class=\"xm-select-demo\"></div>\n<button class=\"btn\" id=\"demo1-getValue\">获取选中值</button>\n<pre id=\"demo1-value\"></pre>\n\n<script>\nvar demo1 = xmSelect.render({\n\tel: '#demo1', \n\tlanguage: 'zn',\n\tdata: [\n\t\t{name: '张三', value: 1},\n\t\t{name: '李四', value: 2},\n\t\t{name: '王五', value: 3},\n\t]\n})\n\ndocument.getElementById('demo1-getValue').onclick = function(){\n\t//获取当前多选选中的值\n\tvar selectArr = demo1.getValue();\n\tdocument.getElementById('demo1-value').innerHTML = JSON.stringify(selectArr, null, 2);\n}\n<\/script>\n\n")])])])],2)],1)},i=[function(){var t=this.$createElement,e=this._self._c||t;return e("h2",{attrs:{id:"an-zhuang"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#an-zhuang"}},[this._v("¶")]),this._v(" 安装")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"jian-jie"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#jian-jie"}},[this._v("¶")]),this._v(" 简介")])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"tip"},[n("p",[t._v("始于 "),n("a",{attrs:{href:"https://layui.com"}},[t._v("layui")]),t._v(" 的一个多选解决方案。"),n("br"),t._v("\n前身 "),n("a",{attrs:{href:"https://github.com/hnzzmsf/layui-formSelects/"}},[t._v("formSelects")]),t._v(", 由于渲染速度慢, 代码冗余, 被放弃了"),n("br"),n("a",{attrs:{href:"https://gitee.com/maplemei/xm-select"}},[t._v("xm-select")]),t._v("使用了新的开发方式, 利用preact进行渲染, 大幅度提高渲染速度, 并且可以灵活拓展")])])},function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("ul",[n("li",[t._v("唯一依赖库"),n("a",{attrs:{href:"https://preactjs.com/"}},[t._v("preactjs")])]),n("li",[t._v("打包方式"),n("a",{attrs:{href:"https://www.webpackjs.com/"}},[t._v("webpack")])]),n("li",[t._v("文档借鉴于"),n("a",{attrs:{href:"https://element.eleme.cn/#/zh-CN"}},[t._v("ElementUI")]),t._v("的编写方式")]),n("li",[n("a",{attrs:{href:"https://fly.layui.com/jie/57776/"}},[t._v("Fly社区交流贴")])]),n("li",[t._v("xm-select技术群①: "),n("strong",[t._v("660408068")]),t._v(" (500人) "),n("a",{attrs:{target:"_blank",href:"https://qm.qq.com/cgi-bin/qm/qr?k=BD2HaEzKyK8IZqI0E3OABxuxZEdk4jOv&jump_from=webapi"}},[n("img",{attrs:{border:"0",src:"//pub.idqqimg.com/wpa/images/group.png",alt:"xm-select技术群①",title:"xm-select技术群①"}})])]),n("li",[t._v("xm-select技术群②: "),n("strong",[t._v("938624691")]),t._v(" (500人) "),n("a",{attrs:{target:"_blank",href:"https://qm.qq.com/cgi-bin/qm/qr?k=iBguKjw2qKcijuJL98PxaVnMu-BKZ8A7&jump_from=webapi"}},[n("img",{attrs:{border:"0",src:"//pub.idqqimg.com/wpa/images/group.png",alt:"xm-select技术群②",title:"xm-select技术群②"}})])]),n("li",[t._v("xm-select技术群③: "),n("strong",[t._v("1145047250")]),t._v(" (500人) "),n("a",{attrs:{target:"_blank",href:"https://qm.qq.com/cgi-bin/qm/qr?k=K71Fc-ynL1jCLMzRPUuoFY94vkXd6o1y&jump_from=webapi"}},[n("img",{attrs:{border:"0",src:"//pub.idqqimg.com/wpa/images/group.png",alt:"xm-select技术群③",title:"xm-select技术群③"}})])]),n("li",[t._v("如果群满了加不进去的话 有问题就提 "),n("a",{attrs:{href:"https://gitee.com/maplemei/xm-select/issues"}},[t._v("issues")]),t._v(" 吧")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"zuo-zhe"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#zuo-zhe"}},[this._v("¶")]),this._v(" 作者")])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"da-shang"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#da-shang"}},[this._v("¶")]),this._v(" 打赏")])},function(){var t=this.$createElement,e=this._self._c||t;return e("p",[e("a",{attrs:{href:"javascript:;"}},[e("img",{staticStyle:{border:"1px solid #EFEFEF"},attrs:{src:n(694),alt:"打赏",width:"300"}})])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"xia-zai"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#xia-zai"}},[this._v("¶")]),this._v(" 下载")])},function(){var t=this.$createElement,e=this._self._c||t;return e("table",[e("thead",[e("tr",[e("th",[this._v("star")]),e("th",[this._v("fork")]),e("th",[this._v("download")])])]),e("tbody",[e("tr",[e("td",[e("a",{attrs:{href:"https://gitee.com/maplemei/xm-select/stargazers"}},[e("img",{attrs:{src:"https://gitee.com/maplemei/xm-select/badge/star.svg?theme=dark",alt:"star"}})])]),e("td",[e("a",{attrs:{href:"https://gitee.com/maplemei/xm-select/members"}},[e("img",{attrs:{src:"https://gitee.com/maplemei/xm-select/badge/fork.svg?theme=dark",alt:"fork"}})])]),e("td",[e("a",{attrs:{href:"https://gitee.com/maplemei/xm-select/releases"}},[this._v("码云下载")])])])])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"er-ci-kai-fa"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#er-ci-kai-fa"}},[this._v("¶")]),this._v(" 二次开发")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("$ git clone https://gitee.com/maplemei/xm-select.git\n$ cd xm-select\n$ npm install && npm run dev\n")])])},function(){var t=this.$createElement,e=this._self._c||t;return e("h3",{attrs:{id:"hello-world"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#hello-world"}},[this._v("¶")]),this._v(" Hello World")])},function(){var t=this.$createElement,e=this._self._c||t;return e("pre",[e("code",[this._v("第一步: 下载\n第二步: 引入 xm-select.js\n第三步: 写一个`<div id=\"demo1\"></div>`\n第四步: 渲染\n\tvar demo1 = xmSelect.render({\n\t\tel: '#demo1',\n\t\tlanguage: 'zn',\n\t\tdata: [\n\t\t\t{name: '张三', value: 1},\n\t\t\t{name: '李四', value: 2},\n\t\t\t{name: '王五', value: 3},\n\t\t]\n\t})\n")])])}];a._withStripped=!0;var s={name:"component-doc",components:{"element-demo0":function(){var t=function(){var t=this.$createElement;this._self._c;return this._m(0)};return t._withStripped=!0,{render:t,staticRenderFns:[function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{staticClass:"xm-select-demo",attrs:{id:"demo1"}}),this._v(" "),e("button",{staticClass:"btn",attrs:{id:"demo1-getValue"}},[this._v("获取选中值")]),this._v(" "),e("pre",{attrs:{id:"demo1-value"}})])}],mounted:function(){this.$nextTick((function(){var t=xmSelect.render({el:"#demo1",language:"zn",data:[{name:"张三",value:1},{name:"李四",value:2},{name:"王五",value:3}]});document.getElementById("demo1-getValue").onclick=function(){var e=t.getValue();document.getElementById("demo1-value").innerHTML=JSON.stringify(e,null,2)}}))}}}()}},r=n(26),l=Object(r.a)(s,a,i,!1,null,null,null);l.options.__file="docs/mds/install.md";e.default=l.exports},740:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement;this._self._c;return this._m(0)};a._withStripped=!0;var i=n(26),s=Object(i.a)({},a,[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[n("h2",{attrs:{id:"es6-yu-fa-shuo-ming"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#es6-yu-fa-shuo-ming"}},[t._v("¶")]),t._v(" es6语法说明")]),n("div",{staticClass:"warning"},[n("p",[t._v("文档中会存在es6语法, 这里简单说明一下, 其中"),n("code",[t._v("IE")]),t._v("不支持"),n("code",[t._v("es6")]),t._v("语法")])]),n("h3",{attrs:{id:"gai-lan"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#gai-lan"}},[t._v("¶")]),t._v(" 概览")]),n("ul",[n("li",[t._v("let const")]),n("li",[t._v("模板字符串")]),n("li",[t._v("对象中属性方法简写")]),n("li",[t._v("箭头函数")]),n("li",[t._v("解构")])]),n("p",[t._v("这里只是简单说明, 有兴趣可以看大神"),n("a",{attrs:{href:"https://es6.ruanyifeng.com/",target:"_blank"}},[t._v("阮一峰的资料")])]),n("h3",{attrs:{id:"let-yuconst"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#let-yuconst"}},[t._v("¶")]),t._v(" let与const")]),n("pre",[n("code",[t._v("//lES6 新增了let命令，用来声明变量。它的用法类似于var，但是所声明的变量，只在let命令所在的代码块内有效。\n\n{\n\tlet a = 10;\n\tvar b = 1;\n}\n\na // ReferenceError: a is not defined.\nb // 1\n\n\n//const声明一个只读的常量。一旦声明，常量的值就不能改变。\nconst PI = 3.1415;\nPI // 3.1415\n\nPI = 3;\n// TypeError: Assignment to constant variable.\n")])]),n("h3",{attrs:{id:"mo-ban-zi-fu-chuan"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#mo-ban-zi-fu-chuan"}},[t._v("¶")]),t._v(" 模板字符串")]),n("pre",[n("code",[t._v('//模板字符串（template string）是增强版的字符串，用反引号（`）标识。它可以当作普通字符串使用，也可以用来定义多行字符串，或者在字符串中嵌入变量。\n\n// 普通字符串\n`In JavaScript \'\\n\' is a line-feed.`\n\n// 多行字符串\n`In JavaScript this is\n not legal.`\n\nconsole.log(`string text line 1\nstring text line 2`);\n\n// 字符串中嵌入变量\nlet name = "Bob", time = "today";\n`Hello ${name}, how are you ${time}?`\n')])]),n("h3",{attrs:{id:"dui-xiang-zhong-shu-xing-fang-fa-jian-xie"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#dui-xiang-zhong-shu-xing-fang-fa-jian-xie"}},[t._v("¶")]),t._v(" 对象中属性方法简写")]),n("pre",[n("code",[t._v("//常规写法\nvar name = 'zs';\n\nvar obj = {\n\tname: name,\n\trun: function(){\n\t\tconsole.log('haha');\n\t}\n}\n\n\n//简写\nlet name = 'zs';\n\nlet obj = {\n\tname,\n\trun(){\n\t\tconsole.log('haha');\n\t}\n}\n")])]),n("h3",{attrs:{id:"jian-tou-han-shu"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#jian-tou-han-shu"}},[t._v("¶")]),t._v(" 箭头函数")]),n("pre",[n("code",[t._v("//常规写法\nsetTimeout(function(){\n\t//...\n}, 1000);\n\n//简写\nsetTimeout(() => {\n\t//...\n}, 1000);\n")])]),n("h3",{attrs:{id:"jie-gou"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#jie-gou"}},[t._v("¶")]),t._v(" 解构")]),n("pre",[n("code",[t._v("//假如现在有一个对象\nlet obj = {\n\tname: 'zs',\n\tage: 18,\n\taddress: 'beijing',\n}\n\n//正常获取name\nvar name = obj.name\n\n//解构写法\nlet { name } = obj\n\n//也可以多个\nlet { name, age, address } = obj;\n\n//如果不存在也可以声明默认值\nlet { name, age, address, status = '1' } = obj;\n\n//想添加一条属性\nobj.status = '1';\n\n//对象属性合并\nvar newObj = {\n\t...obj,\n\tstatus: '1'\n}\n\n//方法传参\nfunction calc(data){\n\tvar a = data.a;\n\tvar b = data.b;\n\treturn a + b;\n}\n//解构传参\nfunction calc({ a, b }){\n\treturn a + b;\n}\n")])]),n("p",[t._v("至于更多的就自己去找学习资料吧")])])}],!1,null,null,null);s.options.__file="docs/mds/es6.md";e.default=s.exports},741:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement;this._self._c;return this._m(0)};a._withStripped=!0;var i=n(26),s=Object(i.a)({},a,[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("section",{staticClass:"content element-doc"},[n("h2",{attrs:{id:"pei-zhi-xiang-yu-fang-fa"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#pei-zhi-xiang-yu-fang-fa"}},[t._v("¶")]),t._v(" 配置项与方法")]),n("h3",{attrs:{id:"pei-zhi-xiang"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#pei-zhi-xiang"}},[t._v("¶")]),t._v(" 配置项")]),n("table",[n("thead",[n("tr",[n("th",[t._v("参数")]),n("th",[t._v("说明")]),n("th",[t._v("类型")]),n("th",[t._v("可选值")]),n("th",[t._v("默认值")])])]),n("tbody",[n("tr",[n("td",[t._v("el")]),n("td",[t._v("渲染对象, css选择器, dom元素")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("language")]),n("td",[t._v("语言选择")]),n("td",[t._v("string")]),n("td",[t._v("zn / en")]),n("td",[t._v("zn")])]),n("tr",[n("td",[t._v("data")]),n("td",[t._v("显示的数据")]),n("td",[t._v("array")]),n("td",[t._v("-")]),n("td",[t._v("[ ]")])]),n("tr",[n("td",[t._v("content")]),n("td",[t._v("自定义下拉框html")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("initValue")]),n("td",[t._v("初始化选中的数据, 需要在data中存在")]),n("td",[t._v("array")]),n("td",[t._v("-")]),n("td",[t._v("null")])]),n("tr",[n("td",[t._v("tips")]),n("td",[t._v("默认提示, 类似于placeholder")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("请选择")])]),n("tr",[n("td",[t._v("empty")]),n("td",[t._v("空数据提示")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("暂无数据")])]),n("tr",[n("td",[t._v("filterable")]),n("td",[t._v("是否开启搜索")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("false")])]),n("tr",[n("td",[t._v("searchTips")]),n("td",[t._v("搜索提示")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("请选择")])]),n("tr",[n("td",[t._v("delay")]),n("td",[t._v("搜索延迟 ms")]),n("td",[t._v("int")]),n("td",[t._v("-")]),n("td",[t._v("500")])]),n("tr",[n("td",[t._v("filterMethod")]),n("td",[t._v("搜索回调函数")]),n("td",[t._v("function(val, item, index, prop)  val: 当前搜索值, item: 每个option选项, index: 位置数据中的下标, prop: 定义key")]),n("td",[t._v("-")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("filterDone")]),n("td",[t._v("搜索完成函数")]),n("td",[t._v("function(val, list)  val: 当前搜索值, list: 过滤后的数据")]),n("td",[t._v("-")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("remoteSearch")]),n("td",[t._v("是否开启自定义搜索 (远程搜索)")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("false")])]),n("tr",[n("td",[t._v("remoteMethod")]),n("td",[t._v("自定义搜索回调函数")]),n("td",[t._v("function(val, cb, show, pageIndex)  val: 当前搜索值, cb(arr, totalPage): 回调函数, 需要回调一个数组, 结构同data, 远程分页需要第二个参数: 总页码, show: 下拉框显示状态, pageIndex: 分页下当前页码")]),n("td",[t._v("-")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("direction")]),n("td",[t._v("下拉方向")]),n("td",[t._v("string")]),n("td",[t._v("auto / up / down")]),n("td",[t._v("auto")])]),n("tr",[n("td",[t._v("style")]),n("td",[t._v("自定义样式")]),n("td",[t._v("object")]),n("td",[t._v("-")]),n("td",[t._v("{ }")])]),n("tr",[n("td",[t._v("height")]),n("td",[t._v("默认最大高度")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("200px")])]),n("tr",[n("td",[t._v("paging")]),n("td",[t._v("是否开启自定义分页")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("false")])]),n("tr",[n("td",[t._v("pageSize")]),n("td",[t._v("分页条数")]),n("td",[t._v("int")]),n("td",[t._v("-")]),n("td",[t._v("10")])]),n("tr",[n("td",[t._v("pageEmptyShow")]),n("td",[t._v("分页无数据是否显示")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("true")])]),n("tr",[n("td",[t._v("pageRemote")]),n("td",[t._v("是否开启远程分页")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("true")])]),n("tr",[n("td",[t._v("radio")]),n("td",[t._v("是否开启单选模式")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("false")])]),n("tr",[n("td",[t._v("repeat")]),n("td",[t._v("是否开启重复性模式")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("false")])]),n("tr",[n("td",[t._v("clickClose")]),n("td",[t._v("是否点击选项后自动关闭下拉框")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("false")])]),n("tr",[n("td",[t._v("prop")]),n("td",[t._v("自定义属性名称, 具体看下表")]),n("td",[t._v("object")]),n("td",[t._v("-")]),n("td")]),n("tr",[n("td",[t._v("theme")]),n("td",[t._v("主题配置, 具体看下表")]),n("td",[t._v("object")]),n("td",[t._v("-")]),n("td")]),n("tr",[n("td",[t._v("model")]),n("td",[t._v("模型, 多选的展示方式, 具体见下表")]),n("td",[t._v("object")]),n("td",[t._v("-")]),n("td")]),n("tr",[n("td",[t._v("iconfont")]),n("td",[t._v("自定义选中图标")]),n("td",[t._v("object")]),n("td",[t._v("-")]),n("td")]),n("tr",[n("td",[t._v("show")]),n("td",[t._v("展开下拉的回调")]),n("td",[t._v("function")]),n("td",[t._v("-")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("hide")]),n("td",[t._v("隐藏下拉的回调")]),n("td",[t._v("function")]),n("td",[t._v("-")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("template")]),n("td",[t._v("自定义渲染选项")]),n("td",[t._v("function({ item, sels, name, value })")]),n("td",[t._v("-")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("on")]),n("td",[t._v("监听选中变化")]),n("td",[t._v("function({ arr, change, isAdd })")]),n("td",[t._v("-")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("max")]),n("td",[t._v("设置多选选中上限")]),n("td",[t._v("int")]),n("td",[t._v("-")]),n("td",[t._v("0")])]),n("tr",[n("td",[t._v("maxMethod")]),n("td",[t._v("达到选中上限的回到")]),n("td",[t._v("function(sels, item), sels: 已选中数据, item: 当前选中的值")]),n("td",[t._v("-")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("name")]),n("td",[t._v("表单提交时的name")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("select")])]),n("tr",[n("td",[t._v("layVerify")]),n("td",[t._v("表单验证, 同layui的lay-verify")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("''")])]),n("tr",[n("td",[t._v("layVerType")]),n("td",[t._v("表单验证, 同layui的lay-verType")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("''")])]),n("tr",[n("td",[t._v("layReqText")]),n("td",[t._v("表单验证, 同layui的lay-reqText")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("''")])]),n("tr",[n("td",[t._v("toolbar")]),n("td",[t._v("工具条, 具体看下表")]),n("td",[t._v("object")]),n("td",[t._v("-")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("showCount")]),n("td",[t._v("展示在下拉框中的最多选项数量")]),n("td",[t._v("int")]),n("td",[t._v("-")]),n("td",[t._v("0")])]),n("tr",[n("td",[t._v("enableKeyboard")]),n("td",[t._v("是否启用键盘操作选项")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("true")])]),n("tr",[n("td",[t._v("enableHoverFirst")]),n("td",[t._v("是否默认选中第一项")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("true")])]),n("tr",[n("td",[t._v("selectedKeyCode")]),n("td",[t._v("选中的键盘KeyCode")]),n("td",[t._v("int")]),n("td",[t._v("全部KeyCode, 也可xmSelect.KeyCode.Enter,xmSelect.KeyCode.Space")]),n("td",[t._v("13")])]),n("tr",[n("td",[t._v("autoRow")]),n("td",[t._v("是否开启自动换行(选项过多时)")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("false")])]),n("tr",[n("td",[t._v("size")]),n("td",[t._v("尺寸")]),n("td",[t._v("string")]),n("td",[t._v("large / medium / small / mini")]),n("td",[t._v("medium")])]),n("tr",[n("td",[t._v("disabled")]),n("td",[t._v("是否禁用多选")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("false")])]),n("tr",[n("td",[t._v("create")]),n("td",[t._v("创建条目")]),n("td",[t._v("function(val, data), val: 搜索的数据, data: 当前下拉数据")]),n("td",[t._v("-")]),n("td",[t._v("null")])]),n("tr",[n("td",[t._v("tree")]),n("td",[t._v("树形结构, 具体看下表")]),n("td",[t._v("object")]),n("td",[t._v("-")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("cascader")]),n("td",[t._v("级联结构, 具体看下表")]),n("td",[t._v("object")]),n("td",[t._v("-")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("submitConversion")]),n("td",[t._v("配置表单提交数据")]),n("td",[t._v("function(sels, prop), sels: 已选中数据, prop: 自定义的prop")]),n("td",[t._v("-")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("done")]),n("td",[t._v("渲染完成回调")]),n("td",[t._v("function")]),n("td",[t._v("-")]),n("td",[t._v("-")])])])]),n("h3",{attrs:{id:"prop"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#prop"}},[t._v("¶")]),t._v(" prop")]),n("table",[n("thead",[n("tr",[n("th",[t._v("参数")]),n("th",[t._v("说明")]),n("th",[t._v("类型")]),n("th",[t._v("可选值")]),n("th",[t._v("默认值")])])]),n("tbody",[n("tr",[n("td",[t._v("name")]),n("td",[t._v("显示名称")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("name")])]),n("tr",[n("td",[t._v("value")]),n("td",[t._v("选中值, 当前多选唯一")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("value")])]),n("tr",[n("td",[t._v("selected")]),n("td",[t._v("是否选中")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("selected")])]),n("tr",[n("td",[t._v("disabled")]),n("td",[t._v("是否禁用")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("disabled")])]),n("tr",[n("td",[t._v("children")]),n("td",[t._v("分组children")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("children")])]),n("tr",[n("td",[t._v("optgroup")]),n("td",[t._v("分组optgroup")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("optgroup")])])])]),n("h3",{attrs:{id:"fen-zu-shuo-ming"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#fen-zu-shuo-ming"}},[t._v("¶")]),t._v(" 分组说明")]),n("p",[t._v("如果children属性为数组的时候开启分组模式")]),n("pre",[n("code",[t._v("{name: '销售员', children: [\n\t{name: '李四', value: 4, selected: true},\n\t{name: '王五', value: 5},\n]},\n\n//可在分组上定义click属性, 来定义点击事件\n{name: '选中', children: [...], click: 'SELECT'},\n{name: '清空', children: [...], click: 'CLEAR'},\n{name: '自动', children: [...], click: 'AUTO'},\n{name: '自定义', children: [...], click: function(item){\n\talert('自定义的, 想干嘛干嘛');\n}},\n")])]),n("h3",{attrs:{id:"theme"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#theme"}},[t._v("¶")]),t._v(" theme")]),n("table",[n("thead",[n("tr",[n("th",[t._v("参数")]),n("th",[t._v("说明")]),n("th",[t._v("类型")]),n("th",[t._v("可选值")]),n("th",[t._v("默认值")])])]),n("tbody",[n("tr",[n("td",[t._v("color")]),n("td",[t._v("主题颜色")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("#009688")])]),n("tr",[n("td",[t._v("maxColor")]),n("td",[t._v("选中上限闪烁边框颜色")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("#e54d42")])]),n("tr",[n("td",[t._v("hover")]),n("td",[t._v("键盘操作的背景色")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("#f2f2f2")])])])]),n("h3",{attrs:{id:"model"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#model"}},[t._v("¶")]),t._v(" model")]),n("p",[t._v("目前仅配置label即可")]),n("pre",[n("code",[t._v("model: {\n\t//是否展示复选框或者单选框图标 show, hidden:变换背景色\n\ticon: 'show',\n\tlabel: {\n\t\t//使用方式\n\t\ttype: 'block',\n\t\t//使用字符串拼接的方式\n\t\ttext: {\n\t\t\t//左边拼接的字符\n\t\t\tleft: '',\n\t\t\t//右边拼接的字符\n\t\t\tright: '',\n\t\t\t//中间的分隔符\n\t\t\tseparator: ', ',\n\t\t},\n\t\t//使用方块显示\n\t\tblock: {\n\t\t\t//最大显示数量, 0:不限制\n\t\t\tshowCount: 0,\n\t\t\t//是否显示删除图标\n\t\t\tshowIcon: true,\n\t\t\t//自定义渲染label, 默认渲染name, 回调参数(item, sels)\n\t\t\ttemplate: null,\n\t\t},\n\t\t//自定义文字\n\t\tcount: {\n\t\t\t//函数处理\n\t\t\ttemplate(data, sels){\n\t\t\t\t//data: 所有的数据\n\t\t\t\t//sels: 选中的数据\n\t\t\t\treturn `已选中 ${sels.length} 项, 共 ${data.length} 项`\n\t\t\t}\n\t\t},\n\t},\n\t//展示类型, 下拉框形式: absolute, 直接显示模式: relative, 浮动布局: fixed\n\ttype: 'absolute', \n},\n")])]),n("h3",{attrs:{id:"iconfont"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#iconfont"}},[t._v("¶")]),t._v(" iconfont")]),n("table",[n("thead",[n("tr",[n("th",[t._v("参数")]),n("th",[t._v("说明")]),n("th",[t._v("类型")]),n("th",[t._v("可选值")]),n("th",[t._v("默认值")])])]),n("tbody",[n("tr",[n("td",[t._v("select")]),n("td",[t._v("选中图标")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("unselect")]),n("td",[t._v("非选中图标")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("half")]),n("td",[t._v("半选图标")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("parent")]),n("td",[t._v("父节点图标, 值为hidden时, 隐藏")]),n("td",[t._v("string")]),n("td",[t._v("-")]),n("td",[t._v("-")])])])]),n("h3",{attrs:{id:"toolbar"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#toolbar"}},[t._v("¶")]),t._v(" toolbar")]),n("table",[n("thead",[n("tr",[n("th",[t._v("参数")]),n("th",[t._v("说明")]),n("th",[t._v("类型")]),n("th",[t._v("可选值")]),n("th",[t._v("默认值")])])]),n("tbody",[n("tr",[n("td",[t._v("show")]),n("td",[t._v("是否展示工具条")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("false")])]),n("tr",[n("td",[t._v("showIcon")]),n("td",[t._v("是否显示工具图标")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("true")])]),n("tr",[n("td",[t._v("list")]),n("td",[t._v("工具条数组 (默认有 全选/清空, 可以自定义), 还有 REVERSE:反选")]),n("td",[t._v("array")]),n("td",[t._v("ALL, CLEAR, REVERSE")]),n("td",[t._v('[ "ALL", "CLEAR" ]')])])])]),n("blockquote",[n("p",[t._v("自定义方式")])]),n("pre",[n("code",[t._v('list: [ "ALL", "CLEAR", \n\t{\n\t\t//显示图标, 可以是layui内置的图标, 也可以是自己引入的图标\n\t\t//传入的icon会转化为 <i class="layui-icon layui-icon-face-smile"></i>\n\t\ticon: \'layui-icon layui-icon-face-smile\',\n\t\t//显示名称 \n\t\tname: \'xxx\',\n\t\t//点击时触发的回调\n\t\tmethod: function(data){\n\t\t\t//data 当前页面的数据\n\t\t\t\n\t\t}\n\t} \n]\n')])]),n("h3",{attrs:{id:"tree"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#tree"}},[t._v("¶")]),t._v(" tree")]),n("table",[n("thead",[n("tr",[n("th",[t._v("参数")]),n("th",[t._v("说明")]),n("th",[t._v("类型")]),n("th",[t._v("可选值")]),n("th",[t._v("默认值")])])]),n("tbody",[n("tr",[n("td",[t._v("show")]),n("td",[t._v("是否展示为树状结构")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("false")])]),n("tr",[n("td",[t._v("showFolderIcon")]),n("td",[t._v("是否显示节点前的三角图标")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("true")])]),n("tr",[n("td",[t._v("showLine")]),n("td",[t._v("是否显示虚线")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("true")])]),n("tr",[n("td",[t._v("indent")]),n("td",[t._v("间距")]),n("td",[t._v("int")]),n("td",[t._v("-")]),n("td",[t._v("20")])]),n("tr",[n("td",[t._v("expandedKeys")]),n("td",[t._v("默认展开的节点数组, 为true时展开所有节点")]),n("td",[t._v("array / boolean")]),n("td",[t._v("-")]),n("td",[t._v("[ ]")])]),n("tr",[n("td",[t._v("strict")]),n("td",[t._v("是否遵循严格父子结构")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("true")])]),n("tr",[n("td",[t._v("simple")]),n("td",[t._v("是否开启极简模式")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("false")])]),n("tr",[n("td",[t._v("nodeType")]),n("td",[t._v("标注节点类型的key")]),n("td",[t._v("string")]),n("td",[t._v("leaf: 叶子节点, parent: 父节点, half: 半选节点")]),n("td",[t._v("__node_type")])]),n("tr",[n("td",[t._v("clickExpand")]),n("td",[t._v("点击节点是否展开, false时点击三角箭头进行展开操作")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("true")])]),n("tr",[n("td",[t._v("clickCheck")]),n("td",[t._v("点击节点是否选中, false时点击复选框进行选中操作")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("true")])])])]),n("h3",{attrs:{id:"cascader"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#cascader"}},[t._v("¶")]),t._v(" cascader")]),n("table",[n("thead",[n("tr",[n("th",[t._v("参数")]),n("th",[t._v("说明")]),n("th",[t._v("类型")]),n("th",[t._v("可选值")]),n("th",[t._v("默认值")])])]),n("tbody",[n("tr",[n("td",[t._v("show")]),n("td",[t._v("是否展示为级联结构")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("false")])]),n("tr",[n("td",[t._v("indent")]),n("td",[t._v("每一级的宽度")]),n("td",[t._v("int")]),n("td",[t._v("-")]),n("td",[t._v("100")])]),n("tr",[n("td",[t._v("strict")]),n("td",[t._v("是否遵循严格父子结构")]),n("td",[t._v("boolean")]),n("td",[t._v("true / false")]),n("td",[t._v("true")])])])]),n("h3",{attrs:{id:"quan-ju-fang-fa"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#quan-ju-fang-fa"}},[t._v("¶")]),t._v(" 全局方法")]),n("table",[n("thead",[n("tr",[n("th",[t._v("事件名")]),n("th",[t._v("说明")]),n("th",[t._v("参数")]),n("th",[t._v("返回值")])])]),n("tbody",[n("tr",[n("td",[t._v("render")]),n("td",[t._v("渲染多选")]),n("td",[t._v("(options: 配置项)")]),n("td",[t._v("实例对象")])]),n("tr",[n("td",[t._v("get")]),n("td",[t._v("获取页面中已经渲染的多选")]),n("td",[t._v("(filter: 过滤"),n("code",[t._v("el")]),t._v(", single: 是否返回单实例)")]),n("td",[t._v("符合条件的实例数组")])]),n("tr",[n("td",[t._v("batch")]),n("td",[t._v("批量操作已渲染的多选")]),n("td",[t._v("(filter: 过滤"),n("code",[t._v("el")]),t._v(", method: 方法, ...方法参数)")]),n("td",[t._v("符合条件的实例数组")])]),n("tr",[n("td",[t._v("arr2tree")]),n("td",[t._v("把列表数据转化为树状结构")]),n("td",[t._v("(arr: 数据, pid: 父节点ID的key, id: 对应key, children: 对应key, topParentId: 顶级节点的ID)")]),n("td",[t._v("符合条件的数组")])])])]),n("pre",[n("code",[t._v("//render 使用方式\nxmSelect.render(OPTIONS);\n\n//get 使用方式\nxmSelect.get('#demo1');\t //指定某一个获取\nxmSelect.get(/.*demo1.*/);  //正则获取\n//自定义方法获取\nxmSelect.get(function(el){\n\treturn el == '#demo1' || el == '#demo2';\n});  \n//单实例\nxmSelect.get('#demo2', true);\n\n//batch 使用方式\n//批量执行禁用\nxmSelect.batch(/.*demo/, 'update', {\n\tdisabled: true,\n});   \n//批量执行warning\nxmSelect.batch(/.*demo/, 'warning', '#F00', true);   \n")])]),n("h3",{attrs:{id:"shi-li-fang-fa"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#shi-li-fang-fa"}},[t._v("¶")]),t._v(" 实例方法")]),n("div",{staticClass:"warning"},[n("p",[t._v("xmSelect.render()后会返回一个xmSelect对象, 可以进行方法调用")])]),n("table",[n("thead",[n("tr",[n("th",[t._v("事件名")]),n("th",[t._v("说明")]),n("th",[t._v("参数")])])]),n("tbody",[n("tr",[n("td",[t._v("getValue")]),n("td",[t._v("获取当前选中的数据")]),n("td",[t._v("(type: 类型), 可选值: name, nameStr, value, valueStr")])]),n("tr",[n("td",[t._v("setValue")]),n("td",[t._v("动态设置数据")]),n("td",[t._v("(array: 选中的数据, show: 是否展开下拉,不传默认当前显示状态,取值: true/false, listenOn: 是否触发on的监听, 默认false)")])]),n("tr",[n("td",[t._v("append")]),n("td",[t._v("追加赋值")]),n("td",[t._v("(array: 追加的数据)")])]),n("tr",[n("td",[t._v("delete")]),n("td",[t._v("删除赋值")]),n("td",[t._v("(array: 删除的数据)")])]),n("tr",[n("td",[t._v("opened")]),n("td",[t._v("主动展开下拉")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("closed")]),n("td",[t._v("主动关闭下拉")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("reset")]),n("td",[t._v("重置为上一次的render状态")]),n("td",[t._v("-")])]),n("tr",[n("td",[t._v("update")]),n("td",[t._v("更新多选选中, reset不保留")]),n("td",[t._v("(options: 见配置项)")])]),n("tr",[n("td",[t._v("warning")]),n("td",[t._v("警告")]),n("td",[t._v("(color: 默认同theme.maxColor, sustain: 是否持续显示)")])]),n("tr",[n("td",[t._v("getTreeValue")]),n("td",[t._v("树节点模式下获取数据, v1.2.0 新增")]),n("td",[t._v("(leafOnly: 是否只是叶子节点，默认值为 false, includeHalfChecked: 是否包含半选节点，默认值为 false)")])]),n("tr",[n("td",[t._v("changeExpandedKeys")]),n("td",[t._v("树模式下更新节点展开状态, v1.2.0 新增")]),n("td",[t._v("(keys: true-全部展开, false-全部关闭, 数组-展开的节点值)")])]),n("tr",[n("td",[t._v("enable")]),n("td",[t._v("启用选项, disabled=false, v1.2.0 新增")]),n("td",[t._v("(array: 想要启用的选项数组)")])]),n("tr",[n("td",[t._v("disable")]),n("td",[t._v("禁用用选项, disabled=true, v1.2.0 新增")]),n("td",[t._v("(array: 想要禁用的选项数组)")])]),n("tr",[n("td",[t._v("calcPosition")]),n("td",[t._v("fixed布局模式下重新计算位置, v1.2.2 新增")]),n("td",[t._v("-")])])])])])}],!1,null,null,null);s.options.__file="docs/mds/options.md";e.default=s.exports},742:function(t,e,n){"use strict";n.r(e);var a=function(){var t=this.$createElement;this._self._c;return this._m(0)},i=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("section",{staticClass:"content element-doc"},[a("h2",{attrs:{id:"chang-jian-wen-ti"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#chang-jian-wen-ti"}},[t._v("¶")]),t._v(" 常见问题")]),a("h3",{attrs:{id:"formselects-yu-xm-select"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#formselects-yu-xm-select"}},[t._v("¶")]),t._v(" formSelects 与 xm-select")]),a("p",[a("a",{attrs:{href:"https://github.com/hnzzmsf/layui-formSelects/"}},[t._v("formSelects")]),t._v("是作者很久以前开发的一款多选插件, 在jQuery时代还是相对比较稳定, 不过性能上有很大的问题。痛并思痛后，开始学习其他开源组件的编写方案，最后决定重新开发。")]),a("p",[a("a",{attrs:{href:"https://gitee.com/maplemei/xm-select"}},[t._v("xm-select")]),t._v("作者精心二次开发的组件, 在formSelects的样式基础上进行了性能优化。目前看来还是比较稳定的 ^_^")]),a("h3",{attrs:{id:"1.-zai-na-li-xia-zai"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#1.-zai-na-li-xia-zai"}},[t._v("¶")]),t._v(" 1.在哪里下载")]),a("p",[a("a",{attrs:{href:"https://gitee.com/maplemei/xm-select/releases"}},[t._v("Gitee码云下载")]),t._v(", 使用时引入"),a("code",[t._v("xm-select.js")]),t._v("即可, 已经内置了css, 具体请看"),a("a",{attrs:{href:"/#/component/install"}},[t._v("入门指南")])]),a("h3",{attrs:{id:"2.-wei-shi-me-duo-xuan-bu-xian-shi"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#2.-wei-shi-me-duo-xuan-bu-xian-shi"}},[t._v("¶")]),t._v(" 2.为什么多选不显示")]),a("p",[t._v("重要的事情说三遍, 需要渲染, 需要渲染, 需要渲染")]),a("h3",{attrs:{id:"3.-xuan-ran-hou-huan-shi-bu-xian-shi"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#3.-xuan-ran-hou-huan-shi-bu-xian-shi"}},[t._v("¶")]),t._v(" 3.渲染后还是不显示")]),a("ul",[a("li",[t._v("打开控制台查看是否报错")]),a("li",[t._v("加群: 660408068, 询问")])]),a("h3",{attrs:{id:"4.-zhan-wei-biao-qian-wei-shi-me-shidiv"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#4.-zhan-wei-biao-qian-wei-shi-me-shidiv"}},[t._v("¶")]),t._v(" 4.占位标签为什么是div")]),a("p",[t._v("演示中使用的是div, 不限制标签, 但是不建议使用"),a("code",[t._v("select")]),t._v(", 因为"),a("code",[t._v("layui")]),t._v("会渲染"),a("code",[t._v("select")]),t._v("标签")]),a("h3",{attrs:{id:"5.-dong-tai-shu-ju-xuan-ran-bao-cuo"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#5.-dong-tai-shu-ju-xuan-ran-bao-cuo"}},[t._v("¶")]),t._v(" 5.动态数据渲染报错")]),a("p",[a("img",{attrs:{src:n(695),alt:""}})]),a("p",[t._v("检查设置的data数据是否为数组类型")]),a("pre",[a("code",[t._v("var demo1 = xmSelect.render({\n\tel: '#demo1', \n\tdata: []\n})\n\n//....N多操作以后\nvar arr = data;//这里的data可能是ajax返回的数据\n\n//这里必须是 [object Array]\nconsole.log(Object.prototype.toString.call(arr));\n//如果是 [object String]\n//1. JSON数据\narr = JSON.parse(arr);\n//2. 类似JSON的数据\narr = eval('(' + arr + ')');\n\ndemo1.update({\n\tdata: arr,\n})\n\n\n")])])])}];a._withStripped=!0;var s=n(26),r=Object(s.a)({},a,i,!1,null,null,null);r.options.__file="docs/mds/question.md";e.default=r.exports},744:function(t,e,n){var a={"./mds/XM01.md":697,"./mds/XM02.md":698,"./mds/XM03.md":699,"./mds/XM04.md":700,"./mds/XM05.md":701,"./mds/XM06.md":702,"./mds/XM07.md":703,"./mds/XM08.md":704,"./mds/XM09.md":705,"./mds/XM10.md":706,"./mds/XM11.md":707,"./mds/XM12.md":708,"./mds/XM13.md":709,"./mds/XM14.md":710,"./mds/XM15.md":711,"./mds/XM16.md":712,"./mds/XM17.md":713,"./mds/XM18.md":714,"./mds/XM19.md":715,"./mds/XM20.md":716,"./mds/XM21.md":717,"./mds/XM22.md":718,"./mds/XM23.md":719,"./mds/XM24.md":720,"./mds/XM25.md":721,"./mds/XM26.md":722,"./mds/XM27.md":723,"./mds/ZM01.md":724,"./mds/ZM02.md":725,"./mds/ZM03.md":726,"./mds/ZM04.md":727,"./mds/ZM05.md":728,"./mds/ZM06.md":729,"./mds/ZM07.md":730,"./mds/ZM08.md":731,"./mds/ZP01.md":732,"./mds/ZP02.md":733,"./mds/ZP03.md":734,"./mds/ZP04.md":735,"./mds/ZP05.md":736,"./mds/ZP06.md":737,"./mds/ZTEST.md":738,"./mds/es6.md":740,"./mds/install.md":739,"./mds/options.md":741,"./mds/question.md":742};function i(t){var e=s(t);return n(e)}function s(t){if(!n.o(a,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return a[t]}i.keys=function(){return Object.keys(a)},i.resolve=s,t.exports=i,i.id=744}}]);