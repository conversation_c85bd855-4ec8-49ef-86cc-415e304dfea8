# 必填规则优化

## 任务需求

优化 schema-mapper.js 中的必填规则逻辑，按照以下新规则实现：

1. 如果字段不存在于required对象中，非必填
2. 如果存在于required字段中，且属性值中不存在enum，只要存在minLength>=0或minimum>=0，则必填
3. 如果存在于required字段中，且属性值中存在enum，则必填

## 实施方案

为了保证逻辑一致性和便于维护，我们将采用以下方案：

1. 在 SchemaToFieldMapper 类中添加一个新的方法 `isFieldRequired`，用于统一判断字段是否必填
2. 修改三处现有的必填判断逻辑，使用新的 `isFieldRequired` 方法
3. 更新相关注释，确保代码文档与实际逻辑一致

## 具体修改

### 1. 添加 isFieldRequired 方法

```javascript
/**
 * 判断字段是否必填
 * @param {boolean} isInRequiredArray - 字段是否在required数组中
 * @param {Object} schema - 字段的schema对象
 * @returns {boolean} 字段是否必填
 */
isFieldRequired(isInRequiredArray, schema) {
  // 如果字段不存在于required对象中，非必填
  if (!isInRequiredArray) {
    return false;
  }
  
  // 如果存在于required字段中，且属性值中存在enum，则必填
  if (schema.enum) {
    return true;
  }
  
  // 如果存在于required字段中，且属性值中不存在enum，只要存在minLength>=0或minimum>=0，则必填
  if ((schema.minLength !== undefined && schema.minLength >= 0) || 
      (schema.minimum !== undefined && schema.minimum >= 0)) {
    return true;
  }
  
  // 其他情况非必填
  return false;
}
```

### 2. 修改三处判断逻辑

1. 在 processField 方法中：
```javascript
// 使用统一的必填判断逻辑
if (this.isFieldRequired(isRequired, fieldSchema)) {
  rootMapping.required = true;
}
```

2. 在 processArrayField 方法中：
```javascript
// 数组元素继承父级必填状态或有 minItems 约束
if (this.isFieldRequired(parentRequired, items) || fieldSchema.minItems > 0) {
  arrayElementMapping.required = true;
}
```

3. 在 processObjectProperties 方法中：
```javascript
// 使用统一的必填判断逻辑
if (this.isFieldRequired(isRequired, propSchema)) {
  propMapping.required = true;
}
```

## 预期效果

1. 统一了必填规则的判断逻辑，避免了重复代码
2. 提高了代码可读性和可维护性
3. 如果未来规则变更，只需修改一处 