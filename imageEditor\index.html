<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Aidge对接</title>
  <style>
    html,
    body {
      height: 100%;
      margin: 0;
    }

    #editor {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
    }

    .aidc-open-frame {
      width: 100%;
      height: 100%;
      border: 0;
    }
  </style>
</head>

<body>
  <div id="editor">
    <iframe class="aidc-open-frame"></iframe>
  </div>

  <script>
    // open sdk 扩展配置
    window.INTL_OP_FRAME_SDK_CONFIG = {
      // API 映射,默认接口/open/api/signature 映射为 /lms/product/selectSignResponse
      apiMap: {
        "/open/api/signature": "/lms/product/selectSignResponse",
      },
    };
    // 需要拼接的参数，以图片翻译为例。其中apiHost,lang为通用参数，其余为能力输入参数。
    const translateParam = {
      apiHost: 'aibcn',
      lang: 'zh-cn',
      // imageUrl: "https://img.alicdn.com/imgextra/i3/O1CN01F84hLR29Futc7Oshz_!!6000000008039-0-tps-750-1000.jpg",
      // sourceLanguage: 'zh',
      targetLanguage: 'en'
    };
    // 处理为拼接字符串
    const payloadString = encodeURIComponent(JSON.stringify(translateParam));

    // 工具页面地址
    const hostUrl = 'https://editor.d.design/editor/index.html/#/';

    // 能力路由
    const router = 'station';

    // 拼接成最终的src字段, 拼接过程注意斜杠符号的使用
    const iframeURL = `${hostUrl}${router}?payload=${payloadString}`;
    //设置iframe的src
    document.querySelector('.aidc-open-frame').src = iframeURL;

  </script>
  <script src="https://g.alicdn.com/code/npm/@ali/intl-op-frame-sdk/0.1.10/main.min.js"></script>
</body>

</html>