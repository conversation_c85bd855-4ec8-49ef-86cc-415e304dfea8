<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>layui相关问题</title>
  <link rel="stylesheet" href="./css/layui.css">
  <link rel="stylesheet" href="./css/formSelects.css">
</head>

<body>
  <div class="layui-container">
    <div class="layui-row">
      <div class="layui-col-md6">
        <h1>layui相关问题</h1>
        <div class="layui-form">
          <div class="layui-form-item">
            <label class="layui-form-label">用户名</label>
            <div class="layui-input-block">
              <input type="text" name="username" lay-verify="required" placeholder="请输入用户名" class="layui-input">
            </div>
          </div>
          <div class="layui-form-item">
            <label class="layui-form-label">店铺</label>
            <div class="layui-input-block">
              <select name="shop" xm-select="shop" xm-select-search xm-select-search-type="dl" xm-select-skin="normal"
                xm-select-page>
              </select>
            </div>
          </div>
          <!-- <div class="layui-form-item">
            <label class="layui-form-label">店铺xm</label>
            <div class="layui-input-block">
              <div id="demo1" class="xm-select-demo"></div>
            </div>
          </div> -->
        </div>
      </div>
    </div>
  </div>
  <script src="./js/layui.all.js"></script>
  <script src="./js/formSelects.js" type="text/javascript"></script>
  <script src="./js/xm-select/xm-select.js" type="text/javascript"></script>
  <script>
    let formSelects = layui.formSelects;
    const $ = layui.jquery;

    $.ajax({
      url: './data/tiktok.json',
      type: 'get',
      success: function (res) {
        if (res.code === '0000') {
          const options = res.data.map(item => ({
            name: item.storeAcct,
            value: item.id
          }));
          // 再渲染数据
          formSelects.data('shop', 'local', {
            arr: options.slice(0, 102)
          });
          // xmSelect.render({
          //   el: '#demo1',
          //   filterable: true,
          //   paging: true,
          //   toolbar: {
          //     show: true,
          //   },
          //   pageSize: 2000,
          //   data: options
          // })
        }
      }
    });
  </script>
</body>

</html>