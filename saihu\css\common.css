﻿.ztt-a {
  color: #428bd4 !important;
  text-decoration: none;
}

.disflex {
  display: flex !important;
}

.brnone {
  border-right: none !important;
}

.ztt-a:hover {
  text-decoration: none;
  color: #235270 !important;
}

.imgSelected {
  border: 3px solid #ff0000 !important;
}

.labelSel {
  padding: 0 15px;
}

.fWhite {
  color: #fff;
}

.fBlack {
  color: #000;
}

.fRed {
  color: red;
}

.fGreen {
  color: green;
}

.fGrey {
  color: grey;
}

.redBtnBg {
  color: rgb(255, 87, 34);
}

.blueBtnBg {
  color: rgb(30, 149, 255);
}

.greenBtnBg {
  color: rgb(0, 155, 136);
}

.pinkBtnBg {
  color: rgb(254, 240, 240);
}

.clearLeft {
  clear: left;
}

.inline_block {
  display: inline-block;
}

.inline_table {
  display: inline-table;
  margin-left: 20px;
}

.formMid {
  width: 100%;
  margin: 0 auto
}

.ovhi {
  overflow: hidden;
}

.w20 {
  width: 20px;
}

.w30 {
  width: 30px;
}

.w40 {
  width: 40px;
}

.w50 {
  width: 50px;
}

.w60 {
  width: 60px;
}

.w90 {
  width: 90px;
}

.w100 {
  width: 100px;
}

.w120 {
  width: 120px;
}

.w130 {
  width: 130px;
}

.w140 {
  width: 140px;
}

.w150 {
  width: 150px;
}

.w180 {
  width: 180px;
}

.w200 {
  width: 200px;
}

.w215 {
  width: 215px;
}

.w300 {
  width: 300px;
}

.w400 {
  width: 400px;
}

.w500 {
  width: 500px;
}

.w_3 {
  width: 3%;
}

.w_5 {
  width: 5%;
}

.w_10 {
  width: 10%;
}

.w_12 {
  width: 12%;
}

.w_13 {
  width: 13%;
}

.w_14 {
  width: 14%;
}

.w_15 {
  width: 15%;
}

.w_50 {
  width: 50%;
}

.w_20 {
  width: 20%;
}

.w_100 {
  width: 100%;
}

.h100 {
  height: 100%;
}

.disN,
.myj-hide {
  display: none;
}

.disNI {
  display: none !important;
}

.p06 {
  padding: 6px
}

.p09 {
  padding: 9px;
}

.p10 {
  padding: 10px;
}

.p20 {
  padding: 20px 40px 0 0;
}

.wp90 {
  width: 90px;
  padding: 9px
}

.w92 {
  width: 92px;
  padding: 9px;
}

.pb10 {
  padding-top: 10px;
  padding-bottom: 10px;
}

.pl20 {
  padding-left: 20px;
}

.pl100 {
  padding-left: 100px;
}

.b1 {
  border: 1px solid #ccc
}

.taRight {
  text-align: right
}

.taLeft {
  text-align: left
}

.taCenter {
  text-align: center
}

.disFCenter {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mt05 {
  margin-top: 5px;
}

.mt10 {
  margin-top: 10px;
}

.mt20 {
  margin-top: 20px
}

.mt30 {
  margin-top: 30px;
}

.mb0 {
  margin-bottom: 0
}

.mb3 {
  margin-bottom: 3px
}

.mb5 {
  margin-bottom: 5px
}

.mb10 {
  margin-bottom: 10px
}

.mb20 {
  margin-bottom: 20px
}

.mb30 {
  margin-bottom: 30px;
}

.ml5 {
  margin-left: 5px
}

.ml10 {
  margin-left: 10px
}

.ml15 {
  margin-left: 15px
}

.ml20 {
  margin-left: 20px
}

.ml40 {
  margin-left: 40px
}

.ml60 {
  margin-left: 60px
}

.ml160 {
  margin-left: 160px
}

.ml200 {
  margin-left: 200px;
}

.mr5 {
  margin-right: 5px;
}

.mr3 {
  margin-right: 3px;
}

.mr10 {
  margin-right: 10px
}

.mr30 {
  margin-right: 30px;
}

.mr100 {
  margin-right: 100px;
}

.lm5 {
  margin-left: -5px
}

.pt30 {
  padding-top: 30px;
}

.pt5 {
  padding-top: 5px;
}

.colorRed {
  color: red;
}

.alertColor {
  background: #FF5722
}

.alertColor p {
  color: #fff
}


/* 固定定位 */

.pora {
  position: relative
}

.poab {
  position: absolute;
  right: 10px;
  top: 15px
}

.poab0 {
  position: absolute;
  right: 0px;
  top: 0px
}

.poab303 {
  position: absolute;
  left: 3px;
  top: 30px
}


/* 树样式设置 */

.treeStyle {
  display: inline-block;
  width: 180px;
  padding: 10px;
  border: 1px solid #ddd;
  overflow: auto;
}

.treeTable {
  display: inline-block;
}

body .layui-tree-skin-shihuang .layui-tree-branch {
  color: #EDCA50;
}


/* 居中 */

.mc {
  margin: 0 auto;
}


/* 字体色 */

.red {
  color: #FF5722
}

.blue {
  color: #1E9FFF
}


/* 浮动 */

.fl {
  float: left;
  margin-right: 5px
}

.fr {
  float: right;
  margin-left: 5px
}


/* 鼠标放置出现下拉框 */

.downdrop {
  position: absolute;
  top: 41px;
  left: 14px;
  z-index: 999999;
  background: #fff;
  width: 200px;
  text-align: center
}


/* 定位 */

.poRight {
  position: absolute;
  top: 10px;
  right: 10px;
}

.poRight1 {
  position: absolute;
  top: 0px;
  right: 10px;
}

.layui-layer-btn {
  border-top: 1px solid #e9e9e9;
}


/*checkbox和主图的间距*/

.checkboxPic {
  width: 100px;
  height: 100px;
  background: skyblue;
  margin-top: 3px
}


/* ul导航栏鼠标点击添加背景色 */

.ulBgColor {
  color: #fff !important;
  background: #0087E0 !important;
}


/*select2多选框*/

#addEditSKU .select2-container {
  width: 190px !important;
}

#addsupplierLayer .select2-container {
  width: 515px !important;
}

.select2-container--default .select2-selection--multiple {
  border-radius: 0 !important;
  border: 1px solid #e6e6e6 !important;
}


/*新增商品模板-颜色模块的input样式*/

.productTpl_span {
  border: 1px solid #ccc;
  padding: 7px 25px;
}


/* 下拉按钮 */

.nav {
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
  display: inline-block;
}

.nav>li {
  position: relative;
  display: block
}

.nav>li>a {
  position: relative;
  display: block;
  padding: 10px 15px
}

.nav>li>a:focus,
.nav>li>a:hover {
  text-decoration: none;
  background-color: #eee
}

.navbar-nav {
  margin: 7.5px -15px
}

.navbar-nav>li>a {
  padding-top: 10px;
  padding-bottom: 10px;
  line-height: 20px
}

@media (max-width:767px) {
  .navbar-nav .open .dropdown-menu {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: transparent;
    border: 0;
    -webkit-box-shadow: none;
    box-shadow: none
  }

  .navbar-nav .open .dropdown-menu .dropdown-header,
  .navbar-nav .open .dropdown-menu>li>a {
    padding: 5px 15px 5px 25px
  }

  .navbar-nav .open .dropdown-menu>li>a {
    line-height: 20px
  }

  .navbar-nav .open .dropdown-menu>li>a:focus,
  .navbar-nav .open .dropdown-menu>li>a:hover {
    background-image: none
  }
}

@media (min-width:768px) {
  .navbar-nav {
    float: left;
    margin: 0
  }

  .navbar-nav>li {
    float: left
  }

  .navbar-nav>li>a {
    padding-top: 15px;
    padding-bottom: 15px
  }
}


/*倒三角*/

.caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top: 4px dashed;
  border-top: 4px solid\9;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent
}


/*下拉选项*/

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 110px;
  padding: 5px 0;
  margin: 2px 0 0;
  font-size: 14px;
  text-align: left;
  list-style: none;
  background-color: #fff;
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, .15);
  border-radius: 4px;
  -webkit-box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
  box-shadow: 0 6px 12px rgba(0, 0, 0, .175)
}

.dropdown-menu>li>a {
  display: block;
  padding: 3px 20px;
  clear: both;
  font-weight: 400;
  line-height: 1.42857143;
  color: #333;
  white-space: nowrap
}

.dropdown-menu>li>a:focus,
.dropdown-menu>li>a:hover {
  color: #262626;
  text-decoration: none;
  background-color: #f5f5f5
}

.dropdown-menu>.active>a,
.dropdown-menu>.active>a:focus,
.dropdown-menu>.active>a:hover {
  color: #fff;
  text-decoration: none;
  background-color: #337ab7;
  outline: 0
}

.navbar-nav>li>.dropdown-menu {
  margin-top: 0;
  border-top-left-radius: 0;
  border-top-right-radius: 0
}


/*展开样式*/

.open>.dropdown-menu {
  display: block
}

.open>a {
  outline: 0
}

.nav .open>a,
.nav .open>a:focus,
.nav .open>a:hover {
  background-color: #eee;
  border-color: #337ab7
}


/*复制事件*/

.copyTxt {
  position: absolute;
  top: 1px;
  left: 20px;
  display: none
}

.copySpan:hover .copyTxt {
  display: block
}

.copySpanOverflow {
  width: 700px;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: inline-block;
  overflow: hidden;
}

.copy-icon {
  cursor: pointer;
}


.copy-icon-after {
  width: 16px;
  height: 16px;
  line-height: 16px;
  display: inline-block;
  cursor: pointer;
  content: '';
  margin-left: 4px;
  background-size: cover;
  background-image: url(../../img/copy-document.png);
}

/*表格样式*/

.layui-table td,
.layui-table th {
  border-color: #e6e6e6 !important;
}

.layui-table th {
  background-color: #f2f2f2 !important
}


/* 状态颜色自定义 */


/*待发布待确认*/

.layui-yellow {
  color: #fbc95d
}


/*待初审 待审核 待组长审核 未开发 开发不全*/

.layui-orange {
  color: #e1935e
}


/*待完善采购信息 待采样*/

.layui-cyan {
  color: #9dd066
}


/*待摄影 待美工 待完成开发 开发中*/

.layui-skyblue {
  color: #4cc0ec
}


/*初审失败 组长审核失败 采样失败 开发失败 不可开发 审核失败*/

.layui-gray {
  color: #dadbd8
}


/*开发完成 开发成功 审核成功*/

.layui-green {
  color: #51bf7e
}


/*自定义侧边菜单*/

.layui-admin-myMenu {
  position: fixed;
  top: 50px;
  left: 0;
  width: 50px;
  height: 100%;
  z-index: 1000;
  background-color: #20222a
}

.layui-admin-myMenu li a {
  color: #ccc;
}

.layui-admin-myMenu ul>li {
  color: #fff;
  width: 50px;
  line-height: 45px;
  text-align: center;
  position: relative;
  box-sizing: border-box;
}

.layui-admin-myMenu ul>li.myMenuhover {
  background-color: #fff;
  color: #000;
  box-sizing: border-box;
}

.layui-admin-myMenu ul>div>li>div>a {
  color: #000 !important;
}

.layui-admin-myMenu .layui-admin-menuMore {
  position: absolute;
  z-index: 1000;
  left: 50px;
  /*top: 0;*/
  background-color: #fff;
  border: 1px #ccc solid;
  border-left: 0 none;
  width: auto;
  padding: 5px 15px;
  box-sizing: border-box;
}

.myMenuHide {
  display: none;
}


/* 分类&品牌页面的样式问题 */

.brandInput {
  width: 70%
}

.brandImg {
  cursor: pointer;
}

.brandPostion1 {
  position: absolute;
  top: 1px;
  right: 15%;
  width: 14%;
  cursor: pointer;
}

.brandPostion2 {
  position: absolute;
  top: 1px;
  right: 0;
  width: 14%;
  cursor: pointer;
}

.relativeContains {
  width: 0;
  height: 0;
  position: absolute;
}


/* 商品列表样式 */

.pointHand {
  cursor: pointer;
}

.linkBox {
  cursor: pointer;
  color: cornflowerblue;
}


/* input输入搜索功能 */

.dimResultDivItem {
  padding: 3px 5px;
  cursor: pointer;
}

.addbg {
  background: #009688;
  color: #fff
}

.dimResultDiv {
  border: solid #e6e6e6 1px;
  box-sizing: border-box;
  border-top: 0;
  background-color: #fff;
  display: none;
  position: absolute;
  z-index: 1000;
  width: 100%;
}

.productlistSearch {
  width: 100%;
  border: 1px solid #e6e6e6;
  box-sizing: border-box;
  position: absolute;
  z-index: 99999;
  background: #fff;
  display: none;
  max-height: 300px;
  overflow-y: scroll;
}

.hp-badge {
  padding: 0 !important;
  text-align: center;
  margin-left: 5px;
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 4px;
  line-height: 20px;
}


/**
换行显示
 */

.showMultiRow {
  white-space: normal;
  word-break: break-all;
  word-wrap: break-word;
}

.borderRed {
  border: 1px solid red;
}

.inputBorderRed {
  border-color: red !important;
}

.inputBorderRed:hover {
  border-color: red !important;
}

.inputBorderRed:focus {
  border-color: red !important;
}

.focusBorder {
  margin: 0;
  padding: 0;
  padding-left: 6px;
  border: 1px solid lightskyblue;
  border-radius: 5px;
}

.h20 {
  height: 20px;
}

.h30 {
  height: 30px;
}

.hv20 {
  height: 20px;
  vertical-align: top
}


/*数量统计处的样式*/

.numCount {
  border: 1px solid #e8e8e8;
  padding: 9px 10px 14px 10px;
  border-bottom: none;
}


/* 给img添加边框类名 */

.imgBorder {
  width: 60px;
  height: 60px;
  border: 1px solid #ccc;
}

.fixedPosition {
  position: fixed !important;
  margin: 0 !important;
  z-index: 20180805;
}


/*自定义分页*/

.customPagination {
  position: fixed;
  bottom: 0;
  width: 50%;
  box-shadow: 0 2px 4px #000;
  background-color: #fff;
  left: 15%;
  padding-left: 15%;
}


/*背景色和鼠标移上去变色*/

.clickBgColor {
  background-color: #00BFFF;
  color: #fff;
}

.hoverBgColor:hover {
  background-color: #00BFFF;
  color: #fff
}


/*自定义input样式表*/

.ivu-input {
  display: inline-block;
  width: 200px;
  height: 32px;
  line-height: 1.5;
  padding: 0 7px;
  border: 1px solid #dcdee2;
  border-radius: 4px;
  color: #515a6e;
  background-color: #fff;
  background-image: none;
  position: relative;
  cursor: text;
  transition: border .2s ease-in-out, background .2s ease-in-out, box-shadow .2s ease-in-out;
  float: left;
  margin-right: 10px
}

.ivu-input:hover {
  border-color: #57a3f3;
}

.cgCountStyle {
  height: 60px;
  background: #fcf8e3;
  margin-top: 20px;
  line-height: 60px;
  padding: 0 10px;
  font-size: 14px;
  border-radius: 10px;
  border: 1px solid #ccc;
}


/*图片的展开收缩功能*/

a.productListSkuShow {
  color: #428bca;
}

.clearfix {
  *zoom: 1;
}


/*IE/6/7*/

.clearfix:after {
  content: "\200B";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}


/*input结合select*/

#ebay_online_searchForm .layui-col-md6.layui-col-lg6 .layui-input.layui-unselect,
#pl_shopee_searchForm .layui-col-md6.layui-col-lg6 .layui-input.layui-unselect {
  border-radius: 0;
  border-right: none;
}

#ebay_online_searchForm .layui-col-md6.layui-col-lg6 .layui-input,
#pl_shopee_searchForm .layui-col-md6.layui-col-lg6 .layui-input {
  border-radius: 0;
}

.inputAndSelect .layui-col-md9.layui-col-lg9 .layui-input {
  border-radius: 0;
  border-right: none;
}

.inputAndSelect .layui-col-md3.layui-col-lg3 .layui-input.layui-unselect {
  border-radius: 0;
}

.layui-form [xm-select-search] {
  display: none !important;
}


/*创建返回顶部按钮元素*/

.returnTop {
  position: fixed;
  width: 30px;
  height: 30px;
  background: #9F9F9F;
  bottom: 10px;
  color: #fff;
  right: 10px;
  z-index: 999;
  text-align: center;
  line-height: 30px;
  cursor: pointer;
}

.hp_InputWithSelect {
  position: absolute;
  z-index: 2;
  width: 80%;
}


/*inputBorder*/

.inputRad {
  border-radius: 0
}

.inputBorRadLeft {
  border-right: none;
  border-radius: 0
}

.inputBorRadRight {
  border-left: none;
  border-radius: 0
}


/*重定义title属性*/

.layTitle:hover::after {
  content: attr(lay-title);
  position: absolute;
  bottom: -15px;
  right: -45px;
  text-align: center;
  height: 15px;
  line-height: 15px;
  font-size: 10px;
  border: 1px solid #767676;
  background-color: #ffffff;
  color: #575757;
  padding: 3px;
  box-shadow: 0 0 5px 1px #ccc;
  z-index: 99999;
}

.canClickEl {
  color: blueviolet;
  cursor: pointer;
}

.canClickEl:hover {
  text-decoration: underline;
}

.alignLeft {
  text-align: left;
}


/*.overContentEllipsis{*/


/*overflow: hidden !important;*/


/*text-overflow:ellipsis !important;*/


/*white-space: normal !important;*/


/*}*/

.overContentEllipsis200px {
  position: relative;
  max-height: 185px;
  overflow: hidden;
}

.overContentEllipsis200px::after {
  content: "...";
  position: absolute;
  bottom: 0;
  right: 0;
  padding-left: 40px;
  background: -webkit-linear-gradient(left, transparent, #fff 55%);
  background: -o-linear-gradient(right, transparent, #fff 55%);
  background: -moz-linear-gradient(right, transparent, #fff 55%);
  background: linear-gradient(to right, transparent, #fff 55%);
}


.overContentEllipsis {
  position: relative;
  line-height: 20px;
  max-height: 40px;
  overflow: hidden;
}

.overContentEllipsis::after {
  content: "...";
  position: absolute;
  bottom: 0;
  right: 0;
  padding-left: 40px;
  background: -webkit-linear-gradient(left, transparent, #fff 55%);
  background: -o-linear-gradient(right, transparent, #fff 55%);
  background: -moz-linear-gradient(right, transparent, #fff 55%);
  background: linear-gradient(to right, transparent, #fff 55%);
}

.overContentEllipsis4 {
  position: relative;
  line-height: 20px;
  max-height: 80px;
  overflow: hidden;
}

.overContentEllipsis4::after {
  content: "...";
  position: absolute;
  bottom: 0;
  right: 0;
  padding-left: 40px;
  background: -webkit-linear-gradient(left, transparent, #fff 55%);
  background: -o-linear-gradient(right, transparent, #fff 55%);
  background: -moz-linear-gradient(right, transparent, #fff 55%);
  background: linear-gradient(to right, transparent, #fff 55%);
}


/*图片加水印*/

.watermark {
  padding: 20px;
}

.watermark-left {
  float: left;
  width: 400px !important;
  /* border: 1px solid #ccc; */
}

.watermark-right {
  border: 1px solid #ccc;
  margin-left: 450px;
  background: url(/lms/static/img/watermark-base-map.jpg) no-repeat 0 0;
  background-size: 100%;
  overflow: hidden;
}

.watermark-right-1000 {
  width: 1000px;
  height: 1000px;
}

.watermark-right-800 {
  width: 800px;
  height: 800px;
}

.pointer {
  cursor: pointer;
}

.v-top {
  vertical-align: top !important;
}

.relative {
  position: relative !important;
}

.f-center {
  text-align: center !important;
}

.border3 {
  border: 1px solid #ccc !important;
}

.formInputColor {
  display: inline-block;
  border: 1px solid #ccc;
  border-radius: 2px;
  padding: 4px 12px;
  height: 32px;
  width: 100%;
  font-size: 13px;
  line-height: 20px;
  vertical-align: middle;
  color: #555;
  background-color: #fff;
  background-image: none;
}


/*图片水印大小调整*/

.gray-c {
  color: #737679 !important;
}

.pull-right {
  float: right !important;
}

.ui-widget-content {
  z-index: 1000;
}

.m-bottom20 {
  margin-bottom: 20px !important;
}

.m-top10 {
  margin-top: 10px !important;
}

.w250 {
  width: 250px !important;
}

.watermark-right-modal {
  overflow: hidden;
  width: 100%;
  height: 100%;
  background-size: 100%;
}

.watermark-div {
  position: absolute;
  border: 1px dashed gainsboro;
  cursor: all-scroll;
  top: 0;
  left: 0
}

.watermark-div>.watermark-div-fix {
  position: absolute;
  width: 100%;
  height: 100%;
}

.watermark-div>.watermark-div-img {
  width: 100%;
  height: 100%;
}

.divBot {
  width: 6px;
  height: 6px;
  border: 1px solid gray;
  position: absolute;
  opacity: 0;
  z-index: 1;
}


/* 水印字体 */

.watermark-font-div {
  min-width: 20px;
  min-height: 20px;
  border: 1px dashed #ccc;
  cursor: all-scroll;
  position: absolute;
  top: 0;
  left: 0;
}

.watermark-font-div>span {
  display: block;
  padding: 10px;
  white-space: nowrap;
  width: 100%;
  height: 100%;
}


/* 滑块样式 */

.ui-corner-all,
.ui-corner-bottom,
.ui-corner-right,
.ui-corner-br {
  border-bottom-right-radius: 4px;
}

.ui-corner-all,
.ui-corner-bottom,
.ui-corner-left,
.ui-corner-bl {
  border-bottom-left-radius: 4px;
}

.ui-corner-all,
.ui-corner-top,
.ui-corner-right,
.ui-corner-tr {
  border-top-right-radius: 4px;
}

.ui-corner-all,
.ui-corner-top,
.ui-corner-left,
.ui-corner-tl {
  border-top-left-radius: 4px;
}

.ui-widget-content {
  border: 1px solid #aaa;
  background: #fff url(../../jquery-ui/images/ui-bg_flat_75_ffffff_40x100.png) 50% 50% repeat-x;
  color: #222;
}

.ui-widget {
  font-family: Verdana, Arial, sans-serif;
  font-size: 1.1em;
}

.ui-slider-horizontal {
  height: .8em;
}

.ui-slider {
  position: relative;
  text-align: left;
}

.ui-slider-horizontal .ui-slider-range-min {
  left: 0;
}

.ui-slider-horizontal .ui-slider-range {
  top: 0;
  height: 100%;
}

.ui-slider .ui-slider-range {
  position: absolute;
  z-index: 1;
  font-size: .7em;
  display: block;
  border: 0;
  background-position: 0 0;
}

.ui-widget-header {
  border: 1px solid #aaa;
  background: #ccc url(../../jquery-ui/images/ui-bg_highlight-soft_75_cccccc_1x100.png) 50% 50% repeat-x;
  color: #222;
  font-weight: bold;
}

.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default {
  border: 1px solid #d3d3d3;
  background: #e6e6e6 url(../../jquery-ui/images/ui-bg_glass_75_e6e6e6_1x400.png) 50% 50% repeat-x;
  font-weight: normal;
  color: #555;
}

.ui-slider-horizontal .ui-slider-handle {
  top: -.3em;
  margin-left: -.6em;
}

.ui-slider .ui-slider-handle {
  position: absolute;
  z-index: 2;
  width: 1.2em;
  height: 1.2em;
  cursor: default;
  -ms-touch-action: none;
  touch-action: none;
}

.ui-slider-horizontal .ui-slider-range {
  top: 0;
  height: 100%;
}

.ui-slider .ui-slider-range {
  position: absolute;
  z-index: 1;
  font-size: .7em;
  display: block;
  border: 0;
  background-position: 0 0;
}

.disAbleInp {
  background-color: rgba(0, 0, 0, 0.1);
}

/*更多查询条件-组件样式 begin*/
.externalContain {
  position: relative;
  width: 0;
  float: left;
  height: 0;
  z-index: 20991231;
}

.externalPop {
  clear: left;
  position: relative;
  left: -20.667vw;
  top: 40px;
  width: 35vw;
  border: 1px solid #e6e6e6;
  background-color: lightyellow;
  padding: 20px 0;
  border-radius: 5px;
  box-shadow: 1px 1px 1px grey;
}

.externalBox {
  width: 85%;
  line-height: 32px;
  text-align: center;
  border: 1px solid #e6e6e6;
  margin-left: 15%;
  cursor: pointer;
  -moz-user-select: none;
  -khtml-user-select: none;
  user-select: none;
  font-style: italic;
}

.externalBox:hover {
  border: 1px solid grey;
}

.showExternal {
  border: 1px solid #1E9FFF !important;
}

/*更多查询条件-组件样式 end*/

/*折叠按钮样式 - begin*/
.btnSelect_hp {
  height: 30px;
  width: 60px;
  line-height: 30px;
  color: grey;
  border-radius: 2px 2px 0 0;
  font-size: 13px;
  text-align: center;
}

.title_btnSelect {
  background-color: #009688;
  color: white;
  border-radius: 2px;
}

.optionBox_btnSelect {
  display: none;
  position: relative;
  z-index: 20240924;
}

.translate_dialog {
  display: none;
  position: absolute;
  background: #fff;
  width: 300px;
  height: 200px;
  padding: 10px 10px;
  left: 120px;
  top: 0px;
  border: 1px solid rgba(111, 111, 111, 0.3);
  background: #fff;
  box-shadow: 5px 5px 5px #999;
}

.mid-tanfer-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

.optionCanvas_btnSelect {
  width: 100%;
  height: auto;
  border: 1px solid rgba(111, 111, 111, 0.3);
  background: #fff;
  box-shadow: 10px 10px 5px #888;
  position: absolute;
}

.option_btnSelect {
  padding: 0;
  height: 30px;
  width: 100%;
  line-height: 30px;
  color: #333;
  z-index: 1000;
  cursor: pointer;
}

.option_imgSelect {
  padding: 0;
  height: 30px;
  width: 100%;
  line-height: 30px;
  color: #000;
  z-index: 1000;
  cursor: pointer;
}

/*折叠按钮样式 - end*/

/*雪碧图小红旗start*/
.flagIcon {
  width: 18px;
  height: 18px;
  display: inline-block;
  vertical-align: middle;
}

.flagRed {
  background: url(../../img/icon.png) no-repeat;
  background-position: -39px -7px;
}

.flagBlue {
  background: url(../../img/icon.png) no-repeat;
  background-position: -71px -7px;
}

.flagGreen {
  background: url(../../img/icon.png) no-repeat;
  background-position: -103px -7px;
}

.flagYellow {
  background: url(../../img/icon.png) no-repeat;
  background-position: -166px -7px;
}

.flagPurple {
  background: url(../../img/icon.png) no-repeat;
  background-position: -135px -7px;
}

.flagWhite {
  background: url(../../img/icon.png) no-repeat;
  background-position: -8px -7px;
}

/*雪碧图小红旗end*/
/*雪碧图评价start*/
.flagAdd {
  background: url(../../img/icon.png) no-repeat;
  background-position: -72px -103px;
}

.flagMinus {
  background: url(../../img/icon.png) no-repeat;
  background-position: -104px -103px;
}

.flagRadio {
  background: url(../../img/icon.png) no-repeat;
  background-position: -39px -103px;
}

/*雪碧图评价end*/

.followRemarkBox {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}


/*下拉按钮*/

.btnpanel {
  position: absolute;
  top: 24px;
  left: 0px;
  background: #fff;
  z-index: 20200615;
  box-shadow: 3px 3px 5px #cccccc;
  min-width: 90px;
}

.sortTitle {
  background: rgb(238, 238, 238);
  color: #aaa;
  padding-left: 5px
}

.leaveLi {
  padding: 5px;
  cursor: pointer;
  word-break: keep-all;
  line-height: 32px;
  height: 32px;
  border-bottom: 1px dashed black;
}

.activedLi {
  background: rgb(110, 183, 120);
  color: #fff;
}

.hoverLi {
  background: rgb(219, 217, 217);
}

.disabledLi {
  pointer-events: none;
  background-color: #FBFBFB;
  color: #C9C9C9;
}

.epeanicon {
  line-height: 30px !important;
  position: static !important;
}

/*下拉按钮*/

/* 固定样式start */
.fixHigh {
  height: 42px;
  line-height: 42px;
  position: relative;
}

.fixTab {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.fixTab>div {
  display: flex;
  line-height: 100%;
}

.fixRight {
  display: flex;
  justify-content: flex-end;
}

/* 固定样式end */
/* 敏姐的固定start */
.zmFixedPage {
  position: fixed;
  background: #fff;
  width: 100%;
  z-index: 99999;
  bottom: 0;
  left: 100px;
  border-top: 1px solid #ccc;
}

/* 敏姐的固定end */

.fieldBox_standard {
  float: left;
  width: 20%;
  height: 25px;
}

.clickToUrl {
  height: 32px;
  line-height: 32px;
  text-align: center;
}

.secondary {
  color: grey;
}

.commonImg-imgsChild {
  margin: 5px;
  border: 1px solid #ccc;
  width: 100px;
  height: 100px;
}

.commonImg-imgsChild>.opte {
  height: 20px;
  text-align: center;
  line-height: 20px;
  background-color: #cccccc;
  margin-top: 3px;
  cursor: pointer;
}

.hidden {
  display: none;
}

/*select2样式*/
.select2-container {
  z-index: 123456789;
}

.select2-container .select2-dropdown.select2-dropdown--below {
  width: 858px !important;
}

.select2-container .select2-selection--multiple {
  box-sizing: border-box;
  cursor: pointer;
  display: block;
  height: 32px;
  width: 858px;
  user-select: none;
  -webkit-user-select: none;
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered {
  box-sizing: border-box;
  list-style: none;
  margin: 0;
  padding: 0 5px;
  width: 100%;
  height: 100%
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
  padding: 1px 5px;
  background-color: #fbfbfb;
  border: 1px solid #e6e6e6;
}

.ztt-card-checkbox {
  height: 40px;
  line-height: 40px;
  margin-left: 10px;
}

/* 分割线样式 */
.lms-splitline {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #000;
  margin-bottom: 15px;
  font-weight: 700;
}

.lms-splitline::before {
  content: '';
  flex: 0.3;
  height: 0;
  border-bottom: #ddd 1px dashed;
}

.lms-splitline::after {
  content: '';
  flex: 9;
  height: 0;
  border-bottom: #ddd 1px dashed;
}

.lms-splitline::before {
  margin-right: 10px;
}

.lms-splitline::after {
  margin-left: 10px;
}

.overContentIntercept {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  cursor: pointer;
}

.captureMP4ModuleUl li,
.capturePictureModuleUl li,
.captureMainImgModuleUl li,
.captureAttrImgModuleUl li,
.captureDetailImgModuleUl li {
  list-style: none;
  height: 235px;
  align-items: center;
  margin: 10px;
  border: 1px solid #ddd;
}

.captureMP4ModuleUl .photo,
.capturePictureModuleUl .photo,
.captureMainImgModuleUl .photo,
.captureAttrImgModuleUl .photo,
.captureDetailImgModuleUl .photo {
  width: 200px;
  height: 200px;
  position: relative;
  /*padding-bottom: 10px;*/
  background: #fff;
  overflow: hidden;
}

.captureMP4ModuleUl .photo-table,
.capturePictureModuleUl .photo-table,
.captureMainImgModuleUl .photo-table,
.captureAttrImgModuleUl .photo-table,
.captureDetailImgModuleUl .photo-table {
  position: absolute;
  width: 100%;
  height: 100%;
  vertical-align: middle;
  text-align: center;
  left: 0;
  top: 0;
}

.captureMP4ModuleUl .photo-tips,
.capturePictureModuleUl .photo-tips,
.captureMainImgModuleUl .photo-tips,
.captureAttrImgModuleUl .photo-tips,
.captureDetailImgModuleUl .photo-tips {
  height: 30px;
  width: 100%;
  line-height: 30px;
  color: #fff;
  background: rgba(0, 0, 0, .5);
  position: absolute;
  bottom: 0;
  padding-left: 10px;
}

.border-r,
.border-l {
  position: relative;
}

.border-r::before {
  content: " ";
  position: absolute;
  border-right: 1px solid #e6ebf7;
  right: 50px;
  height: 18px;
  top: 6px;
}

.border-l::after {
  content: " ";
  position: absolute;
  border-right: 1px solid #e6ebf7;
  left: 40px;
  height: 18px;
  top: 6px;
}

.captureMP4ModuleUl .photo .photo-table [name=imgSrc],
.capturePictureModuleUl .photo .photo-table [name=imgSrc],
.captureMainImgModuleUl .photo .photo-table [name=imgSrc],
.captureAttrImgModuleUl .photo .photo-table [name=imgSrc],
.captureDetailImgModuleUl .photo .photo-table [name=imgSrc] {
  max-width: 100%;
  max-height: 100%;
  vertical-align: middle;
  background-color: #fff;
}

.captureMP4ModuleUl .group,
.capturePictureModuleUl .group,
.captureMainImgModuleUl .group,
.captureAttrImgModuleUl .group,
.captureDetailImgModuleUl .group {
  width: 200px;
  display: flex;
  justify-content: space-between;
  height: 30px;
  line-height: 30px;
}

.waitOrderErrorTips {
  position: absolute;
  top: 2px;
  left: -30px;
  z-index: 2020;
  border: 2px solid #EACCD1;
  background-color: #f7f9fa;
  padding: 5px;
  border-radius: 3px;
  min-width: 500px;
  opacity: 0.8;
}

.waitOrderErrorTipsClose {
  width: 20px;
  height: 20px;
  top: 0;
  background-color: #cccccc;
  opacity: 0.5;
  line-height: 20px;
  color: #fff;
  border-radius: 50%;
  text-align: center;
  position: absolute;
  right: 0;
  margin-left: 20px;
  cursor: pointer;
}

.waitOrderErrorTipsword {
  margin-right: 20px;
  color: #008B8B;
}

.gray {
  color: gray;
}

.plus-red-text {
  font-weight: bold;
  color: red;
  font-size: 13px
}

.plus-layui-badge {
  font-size: 16px;
  height: 26px;
  line-height: 26px;
}

.orderRedStyle {
  font-weight: 700;
  font-size: 24px;
  color: #f00;
}

/* laydate的快捷项 */
.layui-laydate-shortcuts {
  display: inline-block;
  width: 70px;
  border-right: 1px solid #e2e2e2;
  height: 265px;
}

.layui-laydate-range-shortcuts {
  width: 650px !important;
}

.layui-laydate-shortcuts div {
  cursor: pointer;
  padding-left: 10px;
  font-size: 14px;
  text-align: left;
  line-height: 28px;

}

.layui-laydate-shortcuts div:hover {
  color: #5FB878;
}

.common-save-dropdown {
  position: absolute;
  top: 0;
  bottom: 0;
  right: -26px;
  background-color: #1E9FFF;
  color: #fff;
  border-left: 1px solid #fff;
  padding: 5px;
  height: 20px;
  line-height: 20px;
}

.common-save-dropdown-list {
  z-index: 55;
  min-width: 120px;
  max-width: 200px;
  max-height: 300px;
  overflow-y: scroll;
  position: absolute;
  right: -28px;
  line-height: 20px;
  word-wrap: break-word;
  background: #fff;
  outline: none;
  border: 1px solid #e4e7ed;
  padding: 5px 0;
  box-shadow: 0px 0px 12px rgba(0, 0, 0, .4);
}

.common-save-dropdown-list .item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  white-space: nowrap;
  list-style: none;
  line-height: 22px;
  padding: 5px;
  margin: 0;
  cursor: pointer;
  outline: none;
}

.common-save-dropdown-list .item:hover {
  color: #5FB878;
  background-color: rgb(219, 217, 217);
}

.common-save-dropdown-list .item .text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.common-save-dropdown-list .item .layui-icon:hover {
  color: red;
}

.common-save-dropdown-empty {
  padding: 5px;
  text-align: center;
}

.dropdown-datalist {
  position: relative;
  display: inline-block;
}

.dropdown-datalist input {
  padding: 5px;
  border: 1px solid #e6e6e6;
  border-radius: 3px;
  width: 200px;
}

.dropdown-datalist ul {
  position: absolute;
  top: 100%;
  right: 10px;
  margin: 0;
  padding: 0;
  background-color: #fff;
  border: 1px solid #ccc;
  border-top: none;
  border-radius: 0 0 3px 3px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  list-style: none;
  z-index: 1;
  max-height: 600px;
  min-width: 100px;
  overflow-y: auto;
  display: none;
  margin-top: 2px;
  border-radius: 5px;
}

.dropdown-datalist li {
  padding: 6px 8px;
  cursor: pointer;
}

.dropdown-datalist li:hover {
  background-color: #e8e8e9;
}

.dropdown-datalist .show {
  display: block;
}

#layui-tablePlug-col-filter+.layui-border-box.layui-table-view td>div {
  white-space: nowrap;
  /* 禁止文本换行 */
  overflow: hidden;
  /* 隐藏超出容器的文本 */
  text-overflow: ellipsis;
  /* 使用省略号表示被截断的文本 */
}

.searchSupply-li {
  margin: 5px;
  display: flex;
  width: 300px;
}

.searchSupply-li-div {
  margin-left: 5px;
}

.onlyShowFourLine {
  max-height: 85px;
  /* 设置四行文本的最大高度 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
}

.onlyShowThreeLine {
  max-height: 75px;
  /* 设置四行文本的最大高度 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.onlyShowThreeLine2 {
  max-height: 65px;
  /* 设置四行文本的最大高度 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.purcaseOrderNumDetail {
  position: absolute;
  z-index: 20230916;
  width: 200px;
  background: #fff;
  border: 1px solid transparent;
  top: 5%;
  left: 80%;
  box-shadow: 0 0 transparent, 0 0 transparent, 0 0 15px rgba(0, 0, 0, .1);
  padding: 8px 5px;
  height: max-content;
  border-radius: 5px;
  font-size: 12px;
  word-wrap: break-word;
}

.packageNumNumDetail {
  position: absolute;
  z-index: 20230916;
  width: 200px;
  background: #fff;
  border: 1px solid transparent;
  top: 10%;
  left: 30%;
  box-shadow: 0 0 transparent, 0 0 transparent, 0 0 15px rgba(0, 0, 0, .1);
  padding: 8px 5px;
  border-radius: 10px;
  font-size: 12px;
  word-wrap: break-word;
}

.commonPreviewImg {
  display: flex;
  width: 100%;
}

.commonPreviewImg li {
  margin: 5px;
  box-sizing: border-box;
  width: 120px;
  height: 120px;
}

.wordLimitTool {
  position: absolute;
  /* top: 0; */
  /* bottom: 1px; */
  right: 15px;
  /* height: 32px; */
  color: #aaa;
  display: flex;
  background-color: #fff;
  justify-content: space-between;
  align-items: center;
}

.showWordLimit {
  height: 100%;
}

#J_image_editor_dialog {
  z-index: 20981228 !important;
}

/* 首页事项css */
.first-common-matter .matter-body {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  height: 190px;
  overflow-y: auto;
}

.first-common-matter .matter-ul {
  display: flex;
  flex-wrap: wrap;
}

.matterN {
  display: none !important;
}

.first-common-matter .matter-ul li:not(:last-child) {
  margin: 10px 5px;
  width: 120px;
  height: 80px;
  position: relative;
  box-sizing: border-box;
  padding: 5px;
  border-radius: 5px;
  /* box-shadow: 0 0 5px rgba(0, 0, 0, .1); */
}

.first-common-matter .matter-ul li:not(:last-child):hover {
  background-color: #e2f3fd;
}

.first-common-matter .matter-ul li .matter-delete {
  position: absolute;
  top: 0;
  right: 0;
  transform: translateY(-50%);
  display: none;
  cursor: pointer;
  color: #f00;
}

.first-common-matter .matter-icon-handle {
  font-size: 24px;
}

.first-common-matter .matter-ul li .matter-edit {
  right: 30px !important;
  position: absolute;
  top: 0;
  transform: translateY(-50%);
  display: none;
  cursor: pointer;
  color: #1E9FFF;
}

.first-common-matter .matter-ul li:hover .matter-edit,
.first-common-matter .matter-ul li:hover .matter-delete {
  display: block;
}

.first-common-matter .matter-icon {
  margin: 10px 5px;
  width: 120px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  /* background-color: gainsboro; */
  border-radius: 5px;
  cursor: pointer;
}

.first-common-matter .matter-icon-add {
  font-size: 20px;
  line-height: 30px;
}

.first-common-matter .matter-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.first-common-matter .matter-title {
  width: 70px;
  /* 设置元素宽度 */
  white-space: nowrap;
  /* 防止文本换行 */
  overflow: hidden;
  /* 隐藏超出容器的内容 */
  text-overflow: ellipsis;
  /* 显示省略号 */
}

.first-common-matter .matter-link {
  /* font-size: 20px; */
  font-weight: bold;
  margin-top: 10px;
  cursor: pointer;
}

/* 向左旋转90度 */
.rotateLeft90 {
  rotate: -90deg;
}

.input-border-left-0 {
  border-left: 0;
}

.input-border-right-0 {
  border-right: 0;
}

  /* 多行文本溢出隐藏 */
  .comhideOneLine{
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    word-break: break-all;
    position: relative;
}
.comhideOneLine::before{
    float: right;
    height: calc(100% - 24px);
    width: 0;
    content: '';
}
.comhideOneLine::after{
    content: '';
    width: 999vw;
    height: 999vw;
    position: absolute;
    box-shadow: inset calc(30px - 999vw) calc(30px - 999vw) 0 0 #fff;
    margin-left: -30px;
}
.commoreLineText::after{
    visibility: hidden;
}
.commoreLineText .cominfoDetailBtn::after{
    content:'收起'
}
.cominfoDetailBtn{
    float: right;
    color: #1e90fe;
    cursor: pointer;
    clear: both;
}
.cominfoDetailBtn::after{
    content:'展开';
}
