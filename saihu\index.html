<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>JSON Schema 表单生成器</title>
  <link rel="stylesheet" href="./css/layui.css">
  <style>
    .container {
      max-width: 1200px;
      margin: 20px auto;
      padding: 0 15px;
    }
    .form-title {
      font-size: 24px;
      text-align: center;
      margin-bottom: 30px;
      color: #333;
    }
    .layui-form-label {
      width: 180px;
    }
    .layui-input-block {
      margin-left: 210px;
    }
  </style>
</head>
<body>
  <!-- 头部横幅 -->
  <div class="container">
    <div class="form-title">JSON Schema 表单生成器</div>
    
    <!-- 表单容器 -->
    <div id="formContainer">
      <form class="layui-form" action="">
        <!-- 表单内容将由JS动态生成 -->
      </form>
    </div>
  </div>

  <!-- 引入脚本 -->
  <script src="./js/layui.all.js"></script>
  <script src="js/schema-mapper.js"></script>
  <script src="js/form-renderer.js"></script>
  <script>
    // 获取layui的相关模块
    const form = layui.form;
    const layer = layui.layer;
    

    // 获取JSON Schema并渲染表单
    fetch('./dxm.json')
      .then(response => response.json())
      .then(schema => {
        const fields = convertSchemaToFields(schema);
        // 过滤有title属性的字段
        const titleFields = fields.filter(field => field.title);
        console.log('titleFields:', titleFields);
        
        // 调用renderForm函数渲染表单
        renderForm(titleFields, '#formContainer form');
      })
      .catch(error => {
        console.error('加载Schema失败:', error);
        layer.msg('加载Schema失败，请检查网络连接或文件路径');
      });
  </script>
</body>
</html>